//+------------------------------------------------------------------+
//|                                    TestPipelineGroupManager.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 簡單測試流水線類 - 用於測試                                       |
//+------------------------------------------------------------------+
class TestTradingPipeline : public TradingPipeline
{
private:
    bool m_shouldSucceed;
    string m_customMessage;

public:
    TestTradingPipeline(string name,
                       bool shouldSucceed = true,
                       string customMessage = "",
                       string type = "TestTradingPipeline")
        : TradingPipeline(name, type),
          m_shouldSucceed(shouldSucceed),
          m_customMessage(customMessage)
    {
        if(m_customMessage == "")
        {
            m_customMessage = shouldSucceed ? "測試執行成功" : "測試執行失敗";
        }
    }

protected:
    virtual void Main() override
    {
        if(m_shouldSucceed)
        {
            Print("TestTradingPipeline: 成功執行 - " + GetName() + " (" + m_customMessage + ")");
        }
        else
        {
            Print("TestTradingPipeline: 執行失敗 - " + GetName() + " (" + m_customMessage + ")");
        }
    }
};

//+------------------------------------------------------------------+
//| PipelineGroupManager測試類別                                     |
//+------------------------------------------------------------------+
class TestPipelineGroupManager : public TestCase
{
private:
    TestRunner* m_runner;
    bool m_ownsRunner;

public:
    // 構造函數 - 可以接受外部 TestRunner 或創建內部 TestRunner
    TestPipelineGroupManager(TestRunner* externalRunner = NULL) : TestCase("TestPipelineGroupManager")
    {
        if(externalRunner != NULL)
        {
            m_runner = externalRunner;
            m_ownsRunner = false;
        }
        else
        {
            m_runner = new TestRunner();
            m_ownsRunner = true;
        }
    }

    // 析構函數
    ~TestPipelineGroupManager()
    {
        if(m_ownsRunner && m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestAddGroup();
        TestRemoveGroup();
        TestMaxGroupsLimit();
        TestExecuteByEventType();
        TestRestoreByEventType();
        TestFindGroupByName();
        TestGetAllGroups();
        TestClear();
        TestEdgeCases();
        TestComplexScenario();

        // 只有當擁有內部 TestRunner 時才顯示摘要
        if(m_ownsRunner)
        {
            m_runner.ShowSummary();
        }
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("=== 測試構造函數 ===");

        // 測試默認構造函數
        PipelineGroupManager* manager1 = new PipelineGroupManager();
        m_runner.RecordResult(Assert::AssertEquals("默認構造_名稱", "PipelineGroupManager", manager1.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("默認構造_類型", "PipelineGroupManager", manager1.GetType()));
        m_runner.RecordResult(Assert::AssertFalse("默認構造_未執行", manager1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("默認構造_已啟用", manager1.IsEnabled()));
        delete manager1;

        // 測試自定義構造函數
        PipelineGroupManager* manager2 = new PipelineGroupManager("測試管理器", "自定義類型", true);
        m_runner.RecordResult(Assert::AssertEquals("自定義構造_名稱", "測試管理器", manager2.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("自定義構造_類型", "自定義類型", manager2.GetType()));
        delete manager2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("=== 測試基本屬性 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("屬性測試");

        // 測試基本屬性
        m_runner.RecordResult(Assert::AssertEquals("最大組數量", 3, manager.GetMaxGroups()));
        m_runner.RecordResult(Assert::AssertEquals("初始組數量", 0, manager.GetGroupCount()));
        m_runner.RecordResult(Assert::AssertTrue("初始有空位置", manager.HasEmptySlot()));

        // 測試啟用/禁用
        manager.SetEnabled(false);
        m_runner.RecordResult(Assert::AssertFalse("禁用後狀態", manager.IsEnabled()));
        manager.SetEnabled(true);
        m_runner.RecordResult(Assert::AssertTrue("重新啟用後狀態", manager.IsEnabled()));

        delete manager;
    }

    // 測試添加組
    void TestAddGroup()
    {
        Print("=== 測試添加組 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("添加測試");

        // 創建測試組
        PipelineGroup* group1 = new PipelineGroup("組1", "第一個組", TRADING_INIT);
        PipelineGroup* group2 = new PipelineGroup("組2", "第二個組", TRADING_TICK);
        PipelineGroup* group3 = new PipelineGroup("組3", "第三個組", TRADING_DEINIT);

        // 測試添加組
        m_runner.RecordResult(Assert::AssertTrue("添加組1", manager.AddGroup(group1)));
        m_runner.RecordResult(Assert::AssertEquals("添加後組數量1", 1, manager.GetGroupCount()));

        m_runner.RecordResult(Assert::AssertTrue("添加組2", manager.AddGroup(group2)));
        m_runner.RecordResult(Assert::AssertEquals("添加後組數量2", 2, manager.GetGroupCount()));

        m_runner.RecordResult(Assert::AssertTrue("添加組3", manager.AddGroup(group3)));
        m_runner.RecordResult(Assert::AssertEquals("添加後組數量3", 3, manager.GetGroupCount()));
        m_runner.RecordResult(Assert::AssertFalse("滿載後無空位置", manager.HasEmptySlot()));

        // 測試添加第四個組（應該失敗）
        PipelineGroup* group4 = new PipelineGroup("組4", "第四個組", TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("添加第四個組失敗", manager.AddGroup(group4)));
        m_runner.RecordResult(Assert::AssertEquals("仍然是3個組", 3, manager.GetGroupCount()));

        // 測試添加NULL組
        m_runner.RecordResult(Assert::AssertFalse("添加NULL組失敗", manager.AddGroup(NULL)));

        // 測試添加重複名稱的組
        PipelineGroup* duplicateGroup = new PipelineGroup("組1", "重複名稱組", TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("添加重複名稱組失敗", manager.AddGroup(duplicateGroup)));

        delete group4;
        delete duplicateGroup;
        delete manager;
    }

    // 測試移除組
    void TestRemoveGroup()
    {
        Print("=== 測試移除組 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("移除測試");

        // 創建並添加測試組
        PipelineGroup* group1 = new PipelineGroup("移除組1", "第一個組", TRADING_INIT);
        PipelineGroup* group2 = new PipelineGroup("移除組2", "第二個組", TRADING_TICK);

        manager.AddGroup(group1);
        manager.AddGroup(group2);

        // 測試移除組
        m_runner.RecordResult(Assert::AssertTrue("移除組1", manager.RemoveGroup(group1)));
        m_runner.RecordResult(Assert::AssertEquals("移除後組數量", 1, manager.GetGroupCount()));

        m_runner.RecordResult(Assert::AssertTrue("移除組2", manager.RemoveGroup(group2)));
        m_runner.RecordResult(Assert::AssertEquals("全部移除後組數量", 0, manager.GetGroupCount()));
        m_runner.RecordResult(Assert::AssertTrue("移除後有空位置", manager.HasEmptySlot()));

        // 測試移除不存在的組
        PipelineGroup* nonExistentGroup = new PipelineGroup("不存在組", "不存在的組", TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("移除不存在組失敗", manager.RemoveGroup(nonExistentGroup)));

        // 測試移除NULL組
        m_runner.RecordResult(Assert::AssertFalse("移除NULL組失敗", manager.RemoveGroup(NULL)));

        delete nonExistentGroup;
        delete group1;
        delete group2;
        delete manager;
    }

    // 測試最大組數限制
    void TestMaxGroupsLimit()
    {
        Print("=== 測試最大組數限制 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("限制測試");

        // 添加到最大數量
        for(int i = 1; i <= 3; i++)
        {
            PipelineGroup* group = new PipelineGroup("限制組" + IntegerToString(i), "測試組", TRADING_TICK);
            bool added = manager.AddGroup(group);
            m_runner.RecordResult(Assert::AssertTrue("添加組" + IntegerToString(i), added));

            if(!added) delete group;
        }

        m_runner.RecordResult(Assert::AssertEquals("達到最大組數", 3, manager.GetGroupCount()));
        m_runner.RecordResult(Assert::AssertFalse("達到最大後無空位置", manager.HasEmptySlot()));

        // 嘗試添加超過限制的組
        PipelineGroup* extraGroup = new PipelineGroup("額外組", "超過限制的組", TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("超過限制添加失敗", manager.AddGroup(extraGroup)));

        delete extraGroup;
        delete manager;
    }

    // 測試按事件類型執行
    void TestExecuteByEventType()
    {
        Print("=== 測試按事件類型執行 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("執行測試");

        // 創建不同事件類型的組
        PipelineGroup* initGroup = new PipelineGroup("初始化組", "INIT事件組", TRADING_INIT);
        PipelineGroup* tickGroup = new PipelineGroup("Tick組", "TICK事件組", TRADING_TICK);
        PipelineGroup* deinitGroup = new PipelineGroup("清理組", "DEINIT事件組", TRADING_DEINIT);

        // 為每個組添加流水線
        TestTradingPipeline* initPipeline = new TestTradingPipeline("初始化流水線");
        TestTradingPipeline* tickPipeline = new TestTradingPipeline("Tick流水線");
        TestTradingPipeline* deinitPipeline = new TestTradingPipeline("清理流水線");

        CompositePipeline* initComposite = new CompositePipeline("初始化複合");
        CompositePipeline* tickComposite = new CompositePipeline("Tick複合");
        CompositePipeline* deinitComposite = new CompositePipeline("清理複合");

        initComposite.AddPipeline(initPipeline);
        tickComposite.AddPipeline(tickPipeline);
        deinitComposite.AddPipeline(deinitPipeline);

        initGroup.AddPipeline(initComposite);
        tickGroup.AddPipeline(tickComposite);
        deinitGroup.AddPipeline(deinitComposite);

        manager.AddGroup(initGroup);
        manager.AddGroup(tickGroup);
        manager.AddGroup(deinitGroup);

        // 測試執行INIT事件
        manager.Execute(TRADING_INIT);
        m_runner.RecordResult(Assert::AssertTrue("INIT執行後管理器已執行", manager.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("INIT組已執行", initGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertFalse("TICK組未執行", tickGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertFalse("DEINIT組未執行", deinitGroup.IsExecuted()));

        // 重置管理器狀態進行下一次測試
        manager.RestoreAll();

        // 測試執行TICK事件
        manager.Execute(TRADING_TICK);
        m_runner.RecordResult(Assert::AssertTrue("TICK執行後管理器已執行", manager.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("TICK組已執行", tickGroup.IsExecuted()));

        delete manager;
    }

    // 測試按事件類型重置
    void TestRestoreByEventType()
    {
        Print("=== 測試按事件類型重置 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("重置測試");

        // 創建測試組和流水線
        PipelineGroup* group1 = new PipelineGroup("重置組1", "TICK事件組", TRADING_TICK);
        PipelineGroup* group2 = new PipelineGroup("重置組2", "INIT事件組", TRADING_INIT);

        TestTradingPipeline* pipeline1 = new TestTradingPipeline("重置流水線1");
        TestTradingPipeline* pipeline2 = new TestTradingPipeline("重置流水線2");

        CompositePipeline* composite1 = new CompositePipeline("重置複合1");
        CompositePipeline* composite2 = new CompositePipeline("重置複合2");

        composite1.AddPipeline(pipeline1);
        composite2.AddPipeline(pipeline2);

        group1.AddPipeline(composite1);
        group2.AddPipeline(composite2);

        manager.AddGroup(group1);
        manager.AddGroup(group2);

        // 執行兩種事件類型
        manager.Execute(TRADING_TICK);
        manager.RestoreAll();
        manager.Execute(TRADING_INIT);

        // 測試按事件類型重置
        manager.Restore(TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("TICK組已重置", group1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("INIT組仍執行", group2.IsExecuted()));

        manager.Restore(TRADING_INIT);
        m_runner.RecordResult(Assert::AssertFalse("INIT組已重置", group2.IsExecuted()));
        m_runner.RecordResult(Assert::AssertFalse("管理器已重置", manager.IsExecuted()));

        delete manager;
    }

    // 測試按名稱查找組
    void TestFindGroupByName()
    {
        Print("=== 測試按名稱查找組 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("查找測試");

        // 創建測試組
        PipelineGroup* group1 = new PipelineGroup("查找組1", "第一個組", TRADING_TICK);
        PipelineGroup* group2 = new PipelineGroup("查找組2", "第二個組", TRADING_INIT);

        manager.AddGroup(group1);
        manager.AddGroup(group2);

        // 測試查找存在的組
        PipelineGroup* found1 = manager.FindGroupByName("查找組1");
        m_runner.RecordResult(Assert::AssertNotNull("找到組1", found1));
        if(found1 != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("組1名稱正確", "查找組1", found1.GetName()));
        }

        PipelineGroup* found2 = manager.FindGroupByName("查找組2");
        m_runner.RecordResult(Assert::AssertNotNull("找到組2", found2));

        // 測試查找不存在的組
        PipelineGroup* notFound = manager.FindGroupByName("不存在的組");
        m_runner.RecordResult(Assert::AssertNull("未找到不存在組", notFound));

        delete manager;
    }

    // 測試獲取所有組
    void TestGetAllGroups()
    {
        Print("=== 測試獲取所有組 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("獲取所有組測試");

        // 測試空管理器
        PipelineGroup* emptyGroups[];
        int emptyCount = manager.GetAllGroups(emptyGroups);
        m_runner.RecordResult(Assert::AssertEquals("空管理器組數量", 0, emptyCount));

        // 添加組
        PipelineGroup* group1 = new PipelineGroup("全部組1", "第一個組", TRADING_TICK);
        PipelineGroup* group2 = new PipelineGroup("全部組2", "第二個組", TRADING_INIT);

        manager.AddGroup(group1);
        manager.AddGroup(group2);

        // 測試獲取所有組
        PipelineGroup* allGroups[];
        int count = manager.GetAllGroups(allGroups);
        m_runner.RecordResult(Assert::AssertEquals("獲取組數量", 2, count));
        m_runner.RecordResult(Assert::AssertEquals("數組大小", 2, ArraySize(allGroups)));

        // 驗證組的內容
        bool found1 = false, found2 = false;
        for(int i = 0; i < count; i++)
        {
            if(allGroups[i].GetName() == "全部組1") found1 = true;
            if(allGroups[i].GetName() == "全部組2") found2 = true;
        }
        m_runner.RecordResult(Assert::AssertTrue("包含組1", found1));
        m_runner.RecordResult(Assert::AssertTrue("包含組2", found2));

        delete manager;
    }

    // 測試清理功能
    void TestClear()
    {
        Print("=== 測試清理功能 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("清理測試", "清理類型", true);

        // 添加組
        PipelineGroup* group1 = new PipelineGroup("清理組1", "第一個組", TRADING_TICK);
        PipelineGroup* group2 = new PipelineGroup("清理組2", "第二個組", TRADING_INIT);

        manager.AddGroup(group1);
        manager.AddGroup(group2);

        m_runner.RecordResult(Assert::AssertEquals("清理前組數量", 2, manager.GetGroupCount()));

        // 測試清理
        manager.Clear();
        m_runner.RecordResult(Assert::AssertEquals("清理後組數量", 0, manager.GetGroupCount()));
        m_runner.RecordResult(Assert::AssertTrue("清理後有空位置", manager.HasEmptySlot()));
        m_runner.RecordResult(Assert::AssertFalse("清理後未執行", manager.IsExecuted()));

        delete manager;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("=== 測試邊界情況 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("邊界測試");

        // 測試禁用狀態下的執行
        manager.SetEnabled(false);
        PipelineGroup* group = new PipelineGroup("邊界組", "測試組", TRADING_TICK);
        manager.AddGroup(group);

        manager.Execute(TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("禁用時不執行", manager.IsExecuted()));

        // 重新啟用並測試
        manager.SetEnabled(true);
        manager.Execute(TRADING_TICK);
        m_runner.RecordResult(Assert::AssertTrue("啟用後可執行", manager.IsExecuted()));

        // 測試重複執行防護
        bool wasExecuted = manager.IsExecuted();
        manager.Execute(TRADING_TICK);
        m_runner.RecordResult(Assert::AssertTrue("重複執行防護", wasExecuted == manager.IsExecuted()));

        delete manager;
    }

    // 測試複雜場景
    void TestComplexScenario()
    {
        Print("=== 測試複雜場景 ===");

        PipelineGroupManager* manager = new PipelineGroupManager("複雜場景測試");

        // 創建完整的工作流程
        // INIT組
        PipelineGroup* initGroup = new PipelineGroup("初始化工作流程", "系統初始化", TRADING_INIT);
        TestTradingPipeline* configPipeline = new TestTradingPipeline("配置加載");
        TestTradingPipeline* connectionPipeline = new TestTradingPipeline("連接建立");
        CompositePipeline* initComposite = new CompositePipeline("初始化複合");
        initComposite.AddPipeline(configPipeline);
        initComposite.AddPipeline(connectionPipeline);
        initGroup.AddPipeline(initComposite);

        // TICK組
        PipelineGroup* tickGroup = new PipelineGroup("交易工作流程", "主要交易邏輯", TRADING_TICK);
        TestTradingPipeline* dataFeedPipeline = new TestTradingPipeline("數據饋送");
        TestTradingPipeline* signalPipeline = new TestTradingPipeline("信號分析");
        TestTradingPipeline* orderPipeline = new TestTradingPipeline("訂單處理");
        CompositePipeline* tickComposite = new CompositePipeline("交易複合");
        tickComposite.AddPipeline(dataFeedPipeline);
        tickComposite.AddPipeline(signalPipeline);
        tickComposite.AddPipeline(orderPipeline);
        tickGroup.AddPipeline(tickComposite);

        // DEINIT組
        PipelineGroup* deinitGroup = new PipelineGroup("清理工作流程", "系統清理", TRADING_DEINIT);
        TestTradingPipeline* savePipeline = new TestTradingPipeline("狀態保存");
        TestTradingPipeline* cleanupPipeline = new TestTradingPipeline("資源清理");
        CompositePipeline* deinitComposite = new CompositePipeline("清理複合");
        deinitComposite.AddPipeline(savePipeline);
        deinitComposite.AddPipeline(cleanupPipeline);
        deinitGroup.AddPipeline(deinitComposite);

        // 添加所有組到管理器
        manager.AddGroup(initGroup);
        manager.AddGroup(tickGroup);
        manager.AddGroup(deinitGroup);

        // 模擬完整的EA生命週期
        // 1. 初始化
        manager.Execute(TRADING_INIT);
        m_runner.RecordResult(Assert::AssertTrue("複雜場景_初始化執行", initGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertFalse("複雜場景_TICK未執行", tickGroup.IsExecuted()));

        // 2. 重置並執行TICK
        manager.RestoreAll();
        manager.Execute(TRADING_TICK);
        m_runner.RecordResult(Assert::AssertTrue("複雜場景_TICK執行", tickGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertFalse("複雜場景_初始化已重置", initGroup.IsExecuted()));

        // 3. 重置並執行DEINIT
        manager.RestoreAll();
        manager.Execute(TRADING_DEINIT);
        m_runner.RecordResult(Assert::AssertTrue("複雜場景_清理執行", deinitGroup.IsExecuted()));

        // 驗證最終狀態
        m_runner.RecordResult(Assert::AssertEquals("複雜場景_最終組數量", 3, manager.GetGroupCount()));
        m_runner.RecordResult(Assert::AssertEquals("複雜場景_INIT組流水線數", 1, initGroup.GetPipelineCount()));
        m_runner.RecordResult(Assert::AssertEquals("複雜場景_TICK組流水線數", 1, tickGroup.GetPipelineCount()));
        m_runner.RecordResult(Assert::AssertEquals("複雜場景_DEINIT組流水線數", 1, deinitGroup.GetPipelineCount()));

        delete manager;
    }
};
//+------------------------------------------------------------------+

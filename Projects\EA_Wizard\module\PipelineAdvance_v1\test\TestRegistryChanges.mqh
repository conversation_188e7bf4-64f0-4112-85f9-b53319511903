//+------------------------------------------------------------------+
//|                                           TestRegistryChanges.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineRegistry.mqh"
#include "../TradingPipelineContainerManager.mqh"
#include "../TradingPipelineContainer.mqh"
#include "../TradingPipeline.mqh"
#include "../TradingEvent.mqh"

//+------------------------------------------------------------------+
//| 測試註冊器的新功能                                               |
//+------------------------------------------------------------------+
class TestRegistryChanges
{
private:
    TradingPipelineContainerManager* m_manager;
    TradingPipelineRegistry* m_registry;

public:
    //+------------------------------------------------------------------+
    //| 運行所有測試                                                     |
    //+------------------------------------------------------------------+
    void RunAllTests()
    {
        Print("=== 開始測試註冊器修改 ===");

        Setup();
        TestContainerRegistration();
        TestPipelineRegistration();
        TestUnregistration();
        TestExplorerIntegration();
        Cleanup();

        Print("=== 測試完成 ===");
    }

private:
    //+------------------------------------------------------------------+
    //| 設置測試環境                                                     |
    //+------------------------------------------------------------------+
    void Setup()
    {
        Print("--- 設置測試環境 ---");

        m_manager = new TradingPipelineContainerManager("TestManager");
        m_registry = new TradingPipelineRegistry(m_manager, "TestRegistry");

        Print("✓ 測試環境設置完成");
    }

    //+------------------------------------------------------------------+
    //| 測試容器註冊                                                     |
    //+------------------------------------------------------------------+
    void TestContainerRegistration()
    {
        Print("--- 測試容器註冊 ---");

        // 創建不同類型的容器
        TradingPipelineContainer* baseContainer = new TradingPipelineContainer(
            "BaseContainer", "基礎容器"
        );

        EventPipeline* eventContainer = new EventPipeline(
            "EventContainer", TRADING_TICK, "事件容器"
        );

        StagePipeline* stageContainer = new StagePipeline(
            "StageContainer", TICK_DATA_FEED, "階段容器"
        );

        // 測試註冊
        bool result1 = m_registry.Register(TRADING_INIT, baseContainer);
        bool result2 = m_registry.Register(TRADING_TICK, eventContainer);
        bool result3 = m_registry.Register(TICK_DATA_FEED, stageContainer);

        Print("基礎容器註冊結果: ", result1 ? "成功" : "失敗");
        Print("事件容器註冊結果: ", result2 ? "成功" : "失敗");
        Print("階段容器註冊結果: ", result3 ? "成功" : "失敗");

        // 檢查註冊狀態
        Print("TRADING_INIT 已註冊: ", m_registry.IsEventRegistered(TRADING_INIT) ? "是" : "否");
        Print("TRADING_TICK 已註冊: ", m_registry.IsEventRegistered(TRADING_TICK) ? "是" : "否");
        Print("TICK_DATA_FEED 已註冊: ", m_registry.IsStageRegistered(TICK_DATA_FEED) ? "是" : "否");

        Print("✓ 容器註冊測試完成");
    }

    //+------------------------------------------------------------------+
    //| 測試流水線註冊                                                   |
    //+------------------------------------------------------------------+
    void TestPipelineRegistration()
    {
        Print("--- 測試流水線註冊 ---");

        // 創建測試流水線
        TestTradingPipeline* pipeline1 = new TestTradingPipeline(
            "TestPipeline1", TICK_SIGNAL_ANALYSIS, m_registry
        );

        TestTradingPipeline* pipeline2 = new TestTradingPipeline(
            "TestPipeline2", TICK_ORDER_MANAGEMENT, m_registry
        );

        // 測試註冊流水線
        bool result1 = m_registry.Register(pipeline1);
        bool result2 = m_registry.Register(pipeline2);

        Print("流水線1註冊結果: ", result1 ? "成功" : "失敗");
        Print("流水線2註冊結果: ", result2 ? "成功" : "失敗");

        // 檢查容器中的流水線數量
        TradingPipelineContainer* tickContainer = m_manager.GetContainer(TRADING_TICK);
        if(tickContainer != NULL)
        {
            Print("TRADING_TICK 容器中的流水線數量: ", tickContainer.GetPipelineCount());
        }

        Print("✓ 流水線註冊測試完成");
    }

    //+------------------------------------------------------------------+
    //| 測試取消註冊                                                     |
    //+------------------------------------------------------------------+
    void TestUnregistration()
    {
        Print("--- 測試取消註冊 ---");

        // 創建測試流水線
        TestTradingPipeline* pipeline = new TestTradingPipeline(
            "UnregisterTestPipeline", TICK_RISK_CONTROL, m_registry
        );

        // 註冊然後取消註冊
        bool registerResult = m_registry.Register(pipeline);
        Print("註冊結果: ", registerResult ? "成功" : "失敗");

        bool unregisterResult = m_registry.Unregister(pipeline);
        Print("取消註冊結果: ", unregisterResult ? "成功" : "失敗");

        // 檢查容器狀態
        TradingPipelineContainer* tickContainer = m_manager.GetContainer(TRADING_TICK);
        if(tickContainer != NULL)
        {
            Print("取消註冊後容器中的流水線數量: ", tickContainer.GetPipelineCount());
        }

        delete pipeline;
        Print("✓ 取消註冊測試完成");
    }

    //+------------------------------------------------------------------+
    //| 測試探索器集成                                                   |
    //+------------------------------------------------------------------+
    void TestExplorerIntegration()
    {
        Print("--- 測試探索器集成 ---");

        // 創建探索器
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(
            m_registry, "TestExplorer"
        );

        // 測試探索器功能
        Print("探索器是否有效: ", explorer.IsValid() ? "是" : "否");
        Print("探索器註冊器: ", (explorer.GetRegistry() != NULL) ? "有效" : "無效");

        // 測試查詢功能
        ITradingPipeline* tickPipeline = explorer.GetPipeline(TRADING_TICK);
        Print("TRADING_TICK 流水線: ", (tickPipeline != NULL) ? tickPipeline.GetName() : "未找到");

        delete explorer;
        Print("✓ 探索器集成測試完成");
    }

    //+------------------------------------------------------------------+
    //| 清理測試環境                                                     |
    //+------------------------------------------------------------------+
    void Cleanup()
    {
        Print("--- 清理測試環境 ---");

        if(m_registry != NULL)
        {
            delete m_registry;
            m_registry = NULL;
        }

        if(m_manager != NULL)
        {
            delete m_manager;
            m_manager = NULL;
        }

        Print("✓ 測試環境清理完成");
    }
};

//+------------------------------------------------------------------+
//| 測試用的交易流水線類                                             |
//+------------------------------------------------------------------+
class TestTradingPipeline : public TradingPipeline
{
public:
    TestTradingPipeline(string name, ENUM_TRADING_STAGE stage, TradingPipelineRegistry* registry)
        : TradingPipeline(name, "TestTradingPipeline", stage, registry)
    {
    }

protected:
    void Main() override
    {
        Print("執行測試流水線: ", GetName(), ", 階段: ", TradingEventUtils::StageToString(GetStage()));
    }
};

//+------------------------------------------------------------------+
//| 全局函數：運行測試                                               |
//+------------------------------------------------------------------+
void RunRegistryChangesTest()
{
    TestRegistryChanges* test = new TestRegistryChanges();
    test.RunAllTests();
    delete test;
}

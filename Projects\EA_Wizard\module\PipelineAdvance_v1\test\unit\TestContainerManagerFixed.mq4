//+------------------------------------------------------------------+
//|                                        TestContainerManagerFixed.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestTradingPipelineContainerManager.mqh"

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("\n" + StringRepeat("=", 80));
    Print("  測試修正後的 TradingPipelineContainerManager 單元測試");
    Print(StringRepeat("=", 80));
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 80));

    // 運行修正後的測試
    RunTestTradingPipelineContainerManager();

    Print("\n" + StringRepeat("=", 80));
    Print("  修正後的 TradingPipelineContainerManager 單元測試完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 80));
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}

//+------------------------------------------------------------------+
//|                                    TestTradingPipelineRegistry.mq4 |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "../examples/TradingPipelineRegistryExample.mqh"

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== TradingPipelineRegistry 測試開始 ===");
    
    // 運行所有示例
    TradingPipelineRegistryExample::RunAllExamples();
    
    Print("=== TradingPipelineRegistry 測試完成 ===");
}

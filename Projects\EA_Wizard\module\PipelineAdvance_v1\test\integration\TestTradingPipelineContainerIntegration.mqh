//+------------------------------------------------------------------+
//|                      TestTradingPipelineContainerIntegration.mqh |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipeline.mqh"
#include "MockTradingPipeline.mqh"

//+------------------------------------------------------------------+
//| TradingPipelineContainer 和 TradingPipelineContainerManager 整合測試 |
//+------------------------------------------------------------------+
class TradingPipelineContainerIntegrationTest : public TestRunner
{
public:
    TradingPipelineContainerIntegrationTest() : TestRunner() {}

    // 運行所有整合測試
    void RunAllIntegrationTests()
    {
        Print("\n" + StringRepeat("=", 80));
        Print("  TradingPipelineContainer & TradingPipelineContainerManager 整合測試");
        Print(StringRepeat("=", 80));

        // 1. 基本工作流程測試
        TestBasicWorkflow();

        // 2. 複雜容器管理測試
        TestComplexContainerManagement();

        // 3. 事件驅動執行測試
        TestEventDrivenExecution();

        // 4. 大規模場景測試
        TestLargeScaleScenario();

        // 5. 錯誤處理和恢復測試
        TestErrorHandlingAndRecovery();

        // 6. 生命週期管理測試
        TestLifecycleManagement();

        // 7. 性能和併發測試
        TestPerformanceAndConcurrency();

        // 8. 邊界情況測試
        TestEdgeCases();

        // 顯示測試摘要
        ShowSummary();

        Print(StringRepeat("=", 80));
        Print("  TradingPipelineContainer 整合測試完成");
        Print(StringRepeat("=", 80));
    }

private:
    // 1. 基本工作流程測試
    void TestBasicWorkflow()
    {
        Print("\n--- 測試 1: 基本工作流程 ---");

        // 創建容器管理器
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
            "基本工作流程管理器", "BasicWorkflowManager");

        // 創建不同事件類型的容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer(
            "初始化容器", "處理EA初始化", "InitContainer", TRADING_INIT);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer(
            "Tick處理容器", "處理每個Tick", "TickContainer", TRADING_TICK);
        TradingPipelineContainer* deinitContainer = new TradingPipelineContainer(
            "清理容器", "處理EA清理", "DeinitContainer", TRADING_DEINIT);

        // 創建模擬流水線
        MockTradingPipeline* initPipeline1 = MockTradingPipelineFactory::CreateDataFeedPipeline("初始化數據連接");
        MockTradingPipeline* initPipeline2 = MockTradingPipelineFactory::CreateLogPipeline("初始化日誌系統");

        MockTradingPipeline* tickPipeline1 = MockTradingPipelineFactory::CreateSignalPipeline("信號分析");
        MockTradingPipeline* tickPipeline2 = MockTradingPipelineFactory::CreateOrderPipeline("訂單處理");
        MockTradingPipeline* tickPipeline3 = MockTradingPipelineFactory::CreateRiskPipeline("風險控制");

        MockTradingPipeline* deinitPipeline1 = MockTradingPipelineFactory::CreateLogPipeline("保存交易日誌");
        MockTradingPipeline* deinitPipeline2 = new MockTradingPipeline("關閉連接", true, 50, "關閉所有連接");

        // 組裝容器
        initContainer.AddPipeline(initPipeline1);
        initContainer.AddPipeline(initPipeline2);

        tickContainer.AddPipeline(tickPipeline1);
        tickContainer.AddPipeline(tickPipeline2);
        tickContainer.AddPipeline(tickPipeline3);

        deinitContainer.AddPipeline(deinitPipeline1);
        deinitContainer.AddPipeline(deinitPipeline2);

        // 添加容器到管理器
        manager.AddContainer(initContainer);
        manager.AddContainer(tickContainer);
        manager.AddContainer(deinitContainer);

        // 測試基本屬性
        RecordResult(Assert::AssertEquals("基本工作流程_容器數量", 3, manager.GetContainerCount()));
        RecordResult(Assert::AssertEquals("基本工作流程_初始化容器流水線數", 2, initContainer.GetPipelineCount()));
        RecordResult(Assert::AssertEquals("基本工作流程_Tick容器流水線數", 3, tickContainer.GetPipelineCount()));
        RecordResult(Assert::AssertEquals("基本工作流程_清理容器流水線數", 2, deinitContainer.GetPipelineCount()));

        // 模擬EA生命週期執行
        Print("  模擬EA生命週期執行...");

        // 1. 初始化階段
        manager.Execute(TRADING_INIT);
        RecordResult(Assert::AssertTrue("基本工作流程_初始化執行", initContainer.IsExecuted()));
        RecordResult(Assert::AssertFalse("基本工作流程_Tick未執行", tickContainer.IsExecuted()));

        // 2. Tick處理階段（模擬多次Tick）
        for(int i = 0; i < 3; i++)
        {
            tickContainer.Restore();
            manager.Execute(TRADING_TICK);
            RecordResult(Assert::AssertTrue("基本工作流程_Tick執行_" + IntegerToString(i+1), tickContainer.IsExecuted()));
        }

        // 3. 清理階段
        manager.Execute(TRADING_DEINIT);
        RecordResult(Assert::AssertTrue("基本工作流程_清理執行", deinitContainer.IsExecuted()));

        // 驗證執行次數
        RecordResult(Assert::AssertEquals("基本工作流程_信號分析執行次數", 3, tickPipeline1.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("基本工作流程_訂單處理執行次數", 3, tickPipeline2.GetExecutionCount()));

        delete manager;
        Print("✓ 基本工作流程測試完成");
    }

    // 2. 複雜容器管理測試
    void TestComplexContainerManagement()
    {
        Print("\n--- 測試 2: 複雜容器管理 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
            "複雜管理器", "ComplexManager", false, 10);

        // 創建多個不同類型的容器
        TradingPipelineContainer* containers[6];
        containers[0] = new TradingPipelineContainer("預處理容器", "數據預處理", "PreProcessContainer", TRADING_INIT);
        containers[1] = new TradingPipelineContainer("主處理容器", "主要業務邏輯", "MainProcessContainer", TRADING_TICK);
        containers[2] = new TradingPipelineContainer("後處理容器", "數據後處理", "PostProcessContainer", TRADING_TICK);
        containers[3] = new TradingPipelineContainer("監控容器", "系統監控", "MonitorContainer", TRADING_TICK);
        containers[4] = new TradingPipelineContainer("報告容器", "生成報告", "ReportContainer", TRADING_DEINIT);
        containers[5] = new TradingPipelineContainer("備份容器", "數據備份", "BackupContainer", TRADING_DEINIT);

        // 為每個容器添加不同數量的流水線
        for(int i = 0; i < 6; i++)
        {
            int pipelineCount = (i % 3) + 2; // 2-4個流水線
            for(int j = 0; j < pipelineCount; j++)
            {
                string pipelineName = StringFormat("流水線_%d_%d", i, j);
                MockTradingPipeline* pipeline = new MockTradingPipeline(pipelineName, true, 10 + j * 5);
                containers[i].AddPipeline(pipeline);
            }
            manager.AddContainer(containers[i]);
        }

        // 測試容器管理功能
        RecordResult(Assert::AssertEquals("複雜管理_容器總數", 6, manager.GetContainerCount()));

        // 測試按事件類型查找
        TradingPipelineContainer* foundInit = manager.FindContainerByEventType(TRADING_INIT);
        TradingPipelineContainer* foundTick = manager.FindContainerByEventType(TRADING_TICK);
        TradingPipelineContainer* foundDeinit = manager.FindContainerByEventType(TRADING_DEINIT);

        RecordResult(Assert::AssertNotNull("複雜管理_查找INIT容器", foundInit));
        RecordResult(Assert::AssertNotNull("複雜管理_查找TICK容器", foundTick));
        RecordResult(Assert::AssertNotNull("複雜管理_查找DEINIT容器", foundDeinit));

        // 測試按名稱查找
        TradingPipelineContainer* foundByName = manager.FindContainerByName("主處理容器");
        RecordResult(Assert::AssertNotNull("複雜管理_按名稱查找", foundByName));
        if(foundByName != NULL)
        {
            RecordResult(Assert::AssertEquals("複雜管理_名稱匹配", "主處理容器", foundByName.GetName()));
        }

        // 測試容器移除
        bool removed = manager.RemoveContainerByName("監控容器");
        RecordResult(Assert::AssertTrue("複雜管理_移除容器", removed));
        RecordResult(Assert::AssertEquals("複雜管理_移除後數量", 5, manager.GetContainerCount()));

        // 測試批量執行
        manager.ExecuteAll();
        RecordResult(Assert::AssertTrue("複雜管理_批量執行", manager.IsExecuted()));

        delete manager;
        Print("✓ 複雜容器管理測試完成");
    }

    // 3. 事件驅動執行測試
    void TestEventDrivenExecution()
    {
        Print("\n--- 測試 3: 事件驅動執行 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("事件驅動管理器");

        // 創建多個相同事件類型的容器
        TradingPipelineContainer* tickContainer1 = new TradingPipelineContainer("Tick容器1", "", "TickContainer1", TRADING_TICK);
        TradingPipelineContainer* tickContainer2 = new TradingPipelineContainer("Tick容器2", "", "TickContainer2", TRADING_TICK);
        TradingPipelineContainer* tickContainer3 = new TradingPipelineContainer("Tick容器3", "", "TickContainer3", TRADING_TICK);

        // 添加流水線
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("事件測試流水線1", true, 20);
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("事件測試流水線2", true, 30);
        MockTradingPipeline* pipeline3 = new MockTradingPipeline("事件測試流水線3", true, 40);

        tickContainer1.AddPipeline(pipeline1);
        tickContainer2.AddPipeline(pipeline2);
        tickContainer3.AddPipeline(pipeline3);

        manager.AddContainer(tickContainer1);
        manager.AddContainer(tickContainer2);
        manager.AddContainer(tickContainer3);

        // 測試事件驅動執行
        Print("  測試TRADING_TICK事件執行...");
        uint startTime = GetTickCount();
        manager.Execute(TRADING_TICK);
        uint endTime = GetTickCount();

        RecordResult(Assert::AssertTrue("事件驅動_容器1執行", tickContainer1.IsExecuted()));
        RecordResult(Assert::AssertTrue("事件驅動_容器2執行", tickContainer2.IsExecuted()));
        RecordResult(Assert::AssertTrue("事件驅動_容器3執行", tickContainer3.IsExecuted()));

        Print(StringFormat("  事件執行時間: %d 毫秒", endTime - startTime));

        // 測試重置和重複執行
        manager.RestoreAll();
        RecordResult(Assert::AssertFalse("事件驅動_重置後容器1", tickContainer1.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件驅動_重置後容器2", tickContainer2.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件驅動_重置後容器3", tickContainer3.IsExecuted()));

        // 再次執行
        manager.Execute(TRADING_TICK);
        RecordResult(Assert::AssertEquals("事件驅動_重複執行次數1", 2, pipeline1.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("事件驅動_重複執行次數2", 2, pipeline2.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("事件驅動_重複執行次數3", 2, pipeline3.GetExecutionCount()));

        delete manager;
        Print("✓ 事件驅動執行測試完成");
    }

    // 4. 大規模場景測試
    void TestLargeScaleScenario()
    {
        Print("\n--- 測試 4: 大規模場景 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
            "大規模管理器", "LargeScaleManager", false, 20);

        const int containerCount = 10;
        const int pipelinesPerContainer = 15;

        Print(StringFormat("  創建 %d 個容器，每個包含 %d 個流水線...", containerCount, pipelinesPerContainer));

        uint startTime = GetTickCount();

        // 創建大量容器和流水線
        for(int i = 0; i < containerCount; i++)
        {
            string containerName = StringFormat("大規模容器_%d", i + 1);
            ENUM_TRADING_EVENT eventType = (i % 3 == 0) ? TRADING_INIT :
                                          (i % 3 == 1) ? TRADING_TICK : TRADING_DEINIT;

            TradingPipelineContainer* container = new TradingPipelineContainer(
                containerName, "大規模測試容器", "LargeScaleContainer", eventType);

            // 為每個容器添加多個流水線
            for(int j = 0; j < pipelinesPerContainer; j++)
            {
                string pipelineName = StringFormat("大規模流水線_%d_%d", i + 1, j + 1);
                MockTradingPipeline* pipeline = new MockTradingPipeline(pipelineName, true, 1);
                container.AddPipeline(pipeline);
            }

            manager.AddContainer(container);
        }

        uint creationTime = GetTickCount();
        Print(StringFormat("  創建時間: %d 毫秒", creationTime - startTime));

        // 測試大規模執行
        RecordResult(Assert::AssertEquals("大規模_容器數量", containerCount, manager.GetContainerCount()));

        // 執行所有容器
        uint execStartTime = GetTickCount();
        manager.ExecuteAll();
        uint execEndTime = GetTickCount();

        RecordResult(Assert::AssertTrue("大規模_執行完成", manager.IsExecuted()));
        Print(StringFormat("  執行時間: %d 毫秒", execEndTime - execStartTime));
        Print(StringFormat("  總流水線數: %d", containerCount * pipelinesPerContainer));
        Print(StringFormat("  平均每個流水線執行時間: %.2f 毫秒",
              (double)(execEndTime - execStartTime) / (containerCount * pipelinesPerContainer)));

        delete manager;
        Print("✓ 大規模場景測試完成");
    }

    // 5. 錯誤處理和恢復測試
    void TestErrorHandlingAndRecovery()
    {
        Print("\n--- 測試 5: 錯誤處理和恢復 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("錯誤處理管理器");

        // 創建包含成功和失敗流水線的容器
        TradingPipelineContainer* mixedContainer = new TradingPipelineContainer(
            "混合結果容器", "包含成功和失敗的流水線", "MixedContainer", TRADING_TICK);

        // 添加成功和失敗的流水線
        MockTradingPipeline* successPipeline1 = new MockTradingPipeline("成功流水線1", true, 10);
        MockTradingPipeline* failPipeline = new MockTradingPipeline("失敗流水線", false, 15);
        MockTradingPipeline* successPipeline2 = new MockTradingPipeline("成功流水線2", true, 20);
        MockTradingPipeline* exceptionPipeline = MockTradingPipelineFactory::CreateExceptionPipeline("異常流水線");

        mixedContainer.AddPipeline(successPipeline1);
        mixedContainer.AddPipeline(failPipeline);
        mixedContainer.AddPipeline(successPipeline2);
        mixedContainer.AddPipeline(exceptionPipeline);

        manager.AddContainer(mixedContainer);

        // 執行包含錯誤的容器
        Print("  執行包含錯誤的容器...");
        manager.Execute(TRADING_TICK);

        // 驗證容器仍然執行完成（即使包含失敗的流水線）
        RecordResult(Assert::AssertTrue("錯誤處理_容器執行完成", mixedContainer.IsExecuted()));
        RecordResult(Assert::AssertEquals("錯誤處理_成功流水線1執行", 1, successPipeline1.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("錯誤處理_失敗流水線執行", 1, failPipeline.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("錯誤處理_成功流水線2執行", 1, successPipeline2.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("錯誤處理_異常流水線執行", 1, exceptionPipeline.GetExecutionCount()));

        // 測試恢復功能
        Print("  測試恢復功能...");
        mixedContainer.Restore();
        RecordResult(Assert::AssertFalse("錯誤處理_恢復後未執行", mixedContainer.IsExecuted()));

        // 再次執行
        manager.Execute(TRADING_TICK);
        RecordResult(Assert::AssertEquals("錯誤處理_重複執行次數", 2, successPipeline1.GetExecutionCount()));

        delete manager;
        Print("✓ 錯誤處理和恢復測試完成");
    }

    // 6. 生命週期管理測試
    void TestLifecycleManagement()
    {
        Print("\n--- 測試 6: 生命週期管理 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("生命週期管理器");

        // 創建完整的EA生命週期容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("生命週期初始化", "", "LifecycleInit", TRADING_INIT);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("生命週期Tick", "", "LifecycleTick", TRADING_TICK);
        TradingPipelineContainer* deinitContainer = new TradingPipelineContainer("生命週期清理", "", "LifecycleDeinit", TRADING_DEINIT);

        // 初始化階段流水線
        MockTradingPipeline* configPipeline = new MockTradingPipeline("配置加載", true, 30, "加載EA配置");
        MockTradingPipeline* connectionPipeline = new MockTradingPipeline("建立連接", true, 50, "建立數據連接");
        MockTradingPipeline* validationPipeline = new MockTradingPipeline("參數驗證", true, 20, "驗證交易參數");

        // Tick處理階段流水線
        MockTradingPipeline* dataUpdatePipeline = new MockTradingPipeline("數據更新", true, 5, "更新市場數據");
        MockTradingPipeline* strategyPipeline = new MockTradingPipeline("策略執行", true, 15, "執行交易策略");
        MockTradingPipeline* monitorPipeline = new MockTradingPipeline("監控檢查", true, 8, "監控系統狀態");

        // 清理階段流水線
        MockTradingPipeline* savePipeline = new MockTradingPipeline("保存數據", true, 40, "保存交易數據");
        MockTradingPipeline* closePipeline = new MockTradingPipeline("關閉連接", true, 25, "關閉所有連接");

        // 組裝容器
        initContainer.AddPipeline(configPipeline);
        initContainer.AddPipeline(connectionPipeline);
        initContainer.AddPipeline(validationPipeline);

        tickContainer.AddPipeline(dataUpdatePipeline);
        tickContainer.AddPipeline(strategyPipeline);
        tickContainer.AddPipeline(monitorPipeline);

        deinitContainer.AddPipeline(savePipeline);
        deinitContainer.AddPipeline(closePipeline);

        manager.AddContainer(initContainer);
        manager.AddContainer(tickContainer);
        manager.AddContainer(deinitContainer);

        // 模擬完整的EA生命週期
        Print("  模擬完整EA生命週期...");

        // 1. 初始化階段
        Print("    執行初始化階段...");
        uint initStart = GetTickCount();
        manager.Execute(TRADING_INIT);
        uint initEnd = GetTickCount();

        RecordResult(Assert::AssertTrue("生命週期_初始化完成", initContainer.IsExecuted()));
        Print(StringFormat("    初始化耗時: %d 毫秒", initEnd - initStart));

        // 2. 模擬多個Tick處理
        Print("    模擬Tick處理階段...");
        const int tickCount = 10;
        uint totalTickTime = 0;

        for(int i = 0; i < tickCount; i++)
        {
            tickContainer.Restore();
            uint tickStart = GetTickCount();
            manager.Execute(TRADING_TICK);
            uint tickEnd = GetTickCount();
            totalTickTime += (tickEnd - tickStart);
        }

        RecordResult(Assert::AssertEquals("生命週期_Tick執行次數", tickCount, dataUpdatePipeline.GetExecutionCount()));
        Print(StringFormat("    平均Tick處理時間: %.2f 毫秒", (double)totalTickTime / tickCount));

        // 3. 清理階段
        Print("    執行清理階段...");
        uint deinitStart = GetTickCount();
        manager.Execute(TRADING_DEINIT);
        uint deinitEnd = GetTickCount();

        RecordResult(Assert::AssertTrue("生命週期_清理完成", deinitContainer.IsExecuted()));
        Print(StringFormat("    清理耗時: %d 毫秒", deinitEnd - deinitStart));

        // 驗證生命週期完整性
        RecordResult(Assert::AssertEquals("生命週期_配置執行次數", 1, configPipeline.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("生命週期_策略執行次數", tickCount, strategyPipeline.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("生命週期_保存執行次數", 1, savePipeline.GetExecutionCount()));

        delete manager;
        Print("✓ 生命週期管理測試完成");
    }

    // 7. 性能和併發測試
    void TestPerformanceAndConcurrency()
    {
        Print("\n--- 測試 7: 性能和併發 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
            "性能測試管理器", "PerformanceManager", false, 50);

        const int containerCount = 20;
        const int pipelinesPerContainer = 10;
        const int iterations = 5;

        Print(StringFormat("  性能測試參數: %d容器 x %d流水線 x %d迭代",
              containerCount, pipelinesPerContainer, iterations));

        // 創建性能測試容器
        for(int i = 0; i < containerCount; i++)
        {
            string containerName = StringFormat("性能容器_%d", i + 1);
            TradingPipelineContainer* container = new TradingPipelineContainer(
                containerName, "性能測試", "PerformanceContainer", TRADING_TICK);

            for(int j = 0; j < pipelinesPerContainer; j++)
            {
                string pipelineName = StringFormat("性能流水線_%d_%d", i + 1, j + 1);
                MockTradingPipeline* pipeline = new MockTradingPipeline(pipelineName, true, 1);
                container.AddPipeline(pipeline);
            }

            manager.AddContainer(container);
        }

        // 性能測試
        uint totalTime = 0;
        uint minTime = UINT_MAX;
        uint maxTime = 0;

        for(int iter = 0; iter < iterations; iter++)
        {
            manager.RestoreAll();

            uint startTime = GetTickCount();
            manager.ExecuteAll();
            uint endTime = GetTickCount();

            uint iterTime = endTime - startTime;
            totalTime += iterTime;

            if(iterTime < minTime) minTime = iterTime;
            if(iterTime > maxTime) maxTime = iterTime;

            Print(StringFormat("    迭代 %d: %d 毫秒", iter + 1, iterTime));
        }

        double avgTime = (double)totalTime / iterations;
        int totalPipelines = containerCount * pipelinesPerContainer;

        Print(StringFormat("  性能統計:"));
        Print(StringFormat("    總流水線數: %d", totalPipelines));
        Print(StringFormat("    平均執行時間: %.2f 毫秒", avgTime));
        Print(StringFormat("    最快執行時間: %d 毫秒", minTime));
        Print(StringFormat("    最慢執行時間: %d 毫秒", maxTime));
        Print(StringFormat("    平均每流水線時間: %.4f 毫秒", avgTime / totalPipelines));

        // 性能斷言
        RecordResult(Assert::AssertTrue("性能_平均時間合理", avgTime < 5000)); // 5秒內完成
        RecordResult(Assert::AssertTrue("性能_時間變化合理", (maxTime - minTime) < avgTime)); // 變化不超過平均值

        delete manager;
        Print("✓ 性能和併發測試完成");
    }

    // 8. 邊界情況測試
    void TestEdgeCases()
    {
        Print("\n--- 測試 8: 邊界情況 ---");

        // 測試空管理器
        TradingPipelineContainerManager* emptyManager = new TradingPipelineContainerManager("空管理器");

        emptyManager.ExecuteAll();
        RecordResult(Assert::AssertFalse("邊界_空管理器執行", emptyManager.IsExecuted()));
        RecordResult(Assert::AssertTrue("邊界_空管理器為空", emptyManager.IsEmpty()));

        delete emptyManager;

        // 測試單容器單流水線
        TradingPipelineContainerManager* singleManager = new TradingPipelineContainerManager("單一管理器");
        TradingPipelineContainer* singleContainer = new TradingPipelineContainer("單一容器");
        MockTradingPipeline* singlePipeline = new MockTradingPipeline("單一流水線");

        singleContainer.AddPipeline(singlePipeline);
        singleManager.AddContainer(singleContainer);

        singleManager.ExecuteAll();
        RecordResult(Assert::AssertTrue("邊界_單一執行", singleManager.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界_單一執行次數", 1, singlePipeline.GetExecutionCount()));

        delete singleManager;

        // 測試容量限制
        TradingPipelineContainerManager* limitedManager = new TradingPipelineContainerManager(
            "限制管理器", "LimitedManager", false, 2);

        TradingPipelineContainer* container1 = new TradingPipelineContainer("限制容器1");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("限制容器2");
        TradingPipelineContainer* container3 = new TradingPipelineContainer("限制容器3");

        RecordResult(Assert::AssertTrue("邊界_添加容器1", limitedManager.AddContainer(container1)));
        RecordResult(Assert::AssertTrue("邊界_添加容器2", limitedManager.AddContainer(container2)));
        RecordResult(Assert::AssertFalse("邊界_超出限制", limitedManager.AddContainer(container3)));
        RecordResult(Assert::AssertEquals("邊界_限制後數量", 2, limitedManager.GetContainerCount()));

        delete container3;
        delete limitedManager;

        // 測試禁用狀態
        TradingPipelineContainerManager* disabledManager = new TradingPipelineContainerManager("禁用管理器");
        TradingPipelineContainer* disabledContainer = new TradingPipelineContainer("禁用容器");
        MockTradingPipeline* disabledPipeline = new MockTradingPipeline("禁用流水線");

        disabledContainer.AddPipeline(disabledPipeline);
        disabledManager.AddContainer(disabledContainer);

        // 禁用管理器
        disabledManager.SetEnabled(false);
        disabledManager.ExecuteAll();
        RecordResult(Assert::AssertFalse("邊界_禁用管理器不執行", disabledManager.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界_禁用流水線未執行", 0, disabledPipeline.GetExecutionCount()));

        // 重新啟用
        disabledManager.SetEnabled(true);
        disabledManager.ExecuteAll();
        RecordResult(Assert::AssertTrue("邊界_重新啟用執行", disabledManager.IsExecuted()));

        delete disabledManager;

        Print("✓ 邊界情況測試完成");
    }

    // 輔助函數：字符串重複
    string StringRepeat(string str, int count)
    {
        string result = "";
        for(int i = 0; i < count; i++)
        {
            result = result + str;
        }
        return result;
    }
};

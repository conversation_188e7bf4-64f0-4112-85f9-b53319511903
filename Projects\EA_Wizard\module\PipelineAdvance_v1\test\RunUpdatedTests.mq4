//+------------------------------------------------------------------+
//|                                              RunUpdatedTests.mq4 |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| 運行更新後的測試套件                                             |
//| 這個腳本專門用於測試新增和更新的測試模組                         |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 開始執行 PipelineAdvance_v1 更新後的測試套件");
    Print("=" + StringRepeat("=", 60));
    Print("測試時間: " + TimeToString(TimeCurrent()));
    Print("=" + StringRepeat("=", 60));

    // 選項 1: 運行所有測試（包括新增的）
    Print("\n📋 選項 1: 運行所有測試（推薦）");
    RunAllPipelineAdvanceV1Tests();

    Print("\n" + StringRepeat("-", 60));

    // 選項 2: 只運行新增的單元測試
    Print("\n🆕 選項 2: 只運行新增的單元測試");
    RunNewUnitTests();

    Print("\n" + StringRepeat("-", 60));

    // 選項 3: 運行註冊器和探索器專項測試
    Print("\n🔍 選項 3: 註冊器和探索器專項測試");
    RunRegistryExplorerTests();

    Print("\n" + StringRepeat("-", 60));

    // 選項 4: 運行繼承類測試
    Print("\n🧬 選項 4: 繼承類測試");
    RunInheritanceTests();

    Print("\n" + StringRepeat("-", 60));

    // 選項 5: 運行完整模組測試
    Print("\n🔄 選項 5: 完整模組測試");
    RunCompleteModuleTests();

    Print("\n" + StringRepeat("=", 60));
    Print("✅ PipelineAdvance_v1 更新後的測試套件執行完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print("=" + StringRepeat("=", 60));

    // 提供使用建議
    Print("\n💡 使用建議:");
    Print("- 日常開發: 使用 RunNewUnitTests() 快速測試新功能");
    Print("- 功能驗證: 使用 RunRegistryExplorerTests() 測試核心功能");
    Print("- 完整驗證: 使用 RunAllPipelineAdvanceV1Tests() 全面測試");
    Print("- 性能測試: 使用 RunPipelineAdvanceV1PerformanceTests() 性能評估");
}

//+------------------------------------------------------------------+
//| 字符串重複函數（如果 RunAllTests.mqh 中沒有定義）                |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}

//+------------------------------------------------------------------+
//|                                        TestInheritanceClasses.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineContainer.mqh"
#include "../TradingEvent.mqh"

//+------------------------------------------------------------------+
//| 測試新的繼承類 EventPipeline 和 StagePipeline                   |
//+------------------------------------------------------------------+
class TestInheritanceClasses
{
public:
    //+------------------------------------------------------------------+
    //| 運行所有測試                                                     |
    //+------------------------------------------------------------------+
    void RunAllTests()
    {
        Print("=== 開始測試新的繼承類 ===");
        
        TestEventPipeline();
        TestStagePipeline();
        TestPolymorphism();
        
        Print("=== 測試完成 ===");
    }

private:
    //+------------------------------------------------------------------+
    //| 測試 EventPipeline 類                                           |
    //+------------------------------------------------------------------+
    void TestEventPipeline()
    {
        Print("--- 測試 EventPipeline ---");
        
        // 創建 EventPipeline 實例
        EventPipeline* eventPipeline = new EventPipeline(
            "TestEventPipeline",
            TRADING_TICK,
            "測試事件流水線",
            "EventPipeline"
        );
        
        // 測試基本功能
        Print("事件流水線名稱: ", eventPipeline.GetName());
        Print("事件流水線類型: ", eventPipeline.GetType());
        Print("事件類型: ", TradingEventUtils::EventToString(eventPipeline.GetEvent()));
        Print("是否已執行: ", eventPipeline.IsExecuted() ? "是" : "否");
        
        // 測試執行
        eventPipeline.Execute();
        Print("執行後狀態: ", eventPipeline.IsExecuted() ? "已執行" : "未執行");
        
        // 測試重置
        eventPipeline.Restore();
        Print("重置後狀態: ", eventPipeline.IsExecuted() ? "已執行" : "未執行");
        
        delete eventPipeline;
        Print("✓ EventPipeline 測試完成");
    }
    
    //+------------------------------------------------------------------+
    //| 測試 StagePipeline 類                                           |
    //+------------------------------------------------------------------+
    void TestStagePipeline()
    {
        Print("--- 測試 StagePipeline ---");
        
        // 創建 StagePipeline 實例
        StagePipeline* stagePipeline = new StagePipeline(
            "TestStagePipeline",
            TICK_DATA_FEED,
            "測試階段流水線",
            "StagePipeline"
        );
        
        // 測試基本功能
        Print("階段流水線名稱: ", stagePipeline.GetName());
        Print("階段流水線類型: ", stagePipeline.GetType());
        Print("階段類型: ", TradingEventUtils::StageToString(stagePipeline.GetStage()));
        Print("是否已執行: ", stagePipeline.IsExecuted() ? "是" : "否");
        
        // 測試執行
        stagePipeline.Execute();
        Print("執行後狀態: ", stagePipeline.IsExecuted() ? "已執行" : "未執行");
        
        // 測試重置
        stagePipeline.Restore();
        Print("重置後狀態: ", stagePipeline.IsExecuted() ? "已執行" : "未執行");
        
        delete stagePipeline;
        Print("✓ StagePipeline 測試完成");
    }
    
    //+------------------------------------------------------------------+
    //| 測試多態性                                                       |
    //+------------------------------------------------------------------+
    void TestPolymorphism()
    {
        Print("--- 測試多態性 ---");
        
        // 創建不同類型的容器
        TradingPipelineContainer* baseContainer = new TradingPipelineContainer(
            "BaseContainer",
            "基礎容器"
        );
        
        EventPipeline* eventContainer = new EventPipeline(
            "EventContainer",
            TRADING_INIT,
            "事件容器"
        );
        
        StagePipeline* stageContainer = new StagePipeline(
            "StageContainer",
            INIT_START,
            "階段容器"
        );
        
        // 測試多態性 - 都可以作為 ITradingPipeline 使用
        ITradingPipeline* pipelines[3];
        pipelines[0] = baseContainer;
        pipelines[1] = eventContainer;
        pipelines[2] = stageContainer;
        
        Print("多態性測試:");
        for(int i = 0; i < 3; i++)
        {
            Print(StringFormat("  [%d] %s (%s)", 
                             i, 
                             pipelines[i].GetName(), 
                             pipelines[i].GetType()));
            
            pipelines[i].Execute();
            Print(StringFormat("      執行狀態: %s", 
                             pipelines[i].IsExecuted() ? "已執行" : "未執行"));
        }
        
        // 測試動態轉換
        Print("動態轉換測試:");
        for(int i = 0; i < 3; i++)
        {
            EventPipeline* ep = dynamic_cast<EventPipeline*>(pipelines[i]);
            StagePipeline* sp = dynamic_cast<StagePipeline*>(pipelines[i]);
            
            if(ep != NULL)
            {
                Print(StringFormat("  [%d] 是 EventPipeline，事件: %s", 
                                 i, 
                                 TradingEventUtils::EventToString(ep.GetEvent())));
            }
            else if(sp != NULL)
            {
                Print(StringFormat("  [%d] 是 StagePipeline，階段: %s", 
                                 i, 
                                 TradingEventUtils::StageToString(sp.GetStage())));
            }
            else
            {
                Print(StringFormat("  [%d] 是基礎 TradingPipelineContainer", i));
            }
        }
        
        // 清理資源
        delete baseContainer;
        delete eventContainer;
        delete stageContainer;
        
        Print("✓ 多態性測試完成");
    }
};

//+------------------------------------------------------------------+
//| 全局函數：運行測試                                               |
//+------------------------------------------------------------------+
void RunInheritanceClassesTest()
{
    TestInheritanceClasses* test = new TestInheritanceClasses();
    test.RunAllTests();
    delete test;
}

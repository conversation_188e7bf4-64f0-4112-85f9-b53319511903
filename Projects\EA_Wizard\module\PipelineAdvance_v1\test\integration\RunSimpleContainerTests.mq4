//+------------------------------------------------------------------+
//|                                      RunSimpleContainerTests.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "SimpleContainerTestRunner.mqh"

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("\n" + StringRepeat("=", 70));
    Print("  TradingPipelineContainer 簡化整合測試");
    Print(StringRepeat("=", 70));
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 70));

    // 創建簡化整合測試運行器
    SimpleContainerIntegrationTestRunner* runner = new SimpleContainerIntegrationTestRunner();

    // 運行整合測試
    runner.RunIntegrationTests();

    // 清理
    delete runner;

    Print("\n" + StringRepeat("=", 70));
    Print("  TradingPipelineContainer 簡化整合測試完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 70));
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}

//+------------------------------------------------------------------+
//|                                                  CompileTest.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 測試編譯是否成功
#include "../TradingPipelineContainerManager.mqh"
#include "../TradingPipelineContainer.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("編譯測試成功！");

    // 簡單的實例化測試
    TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("測試管理器");
    Print("管理器創建成功: ", manager.GetName());

    TradingPipelineContainer* container = new TradingPipelineContainer("測試容器", "測試描述", "TradingPipelineContainer", TRADING_TICK);
    Print("容器創建成功: ", container.GetName());

    // 測試添加容器到管理器
    bool addResult = manager.AddContainer(container);
    Print("添加容器到管理器: ", addResult ? "成功" : "失敗");

    // 清理
    delete manager;
    delete container;

    Print("所有測試完成！");
}
//+------------------------------------------------------------------+

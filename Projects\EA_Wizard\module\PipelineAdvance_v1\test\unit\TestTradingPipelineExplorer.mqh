//+------------------------------------------------------------------+
//|                                    TestTradingPipelineExplorer.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineExplorer.mqh"
#include "../../TradingPipelineRegistry.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單交易流水線                                           |
//+------------------------------------------------------------------+
class TestExplorerTradingPipeline : public TradingPipeline
{
private:
    string m_testMessage;

public:
    TestExplorerTradingPipeline(string name, string testMessage = "測試執行")
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage)
    {
    }

protected:
    virtual void Main() override
    {
        Print(StringFormat("[%s] 執行: %s", GetName(), m_testMessage));
    }
};

//+------------------------------------------------------------------+
//| TradingPipelineExplorer 測試類                                  |
//+------------------------------------------------------------------+
class TestTradingPipelineExplorerCase : public TestCase
{
private:
    TestRunner* m_runner;
    bool m_ownsRunner;

public:
    // 構造函數 - 可以接受外部 TestRunner 或創建內部 TestRunner
    TestTradingPipelineExplorerCase(TestRunner* externalRunner = NULL) : TestCase("TestTradingPipelineExplorer")
    {
        if(externalRunner != NULL)
        {
            m_runner = externalRunner;
            m_ownsRunner = false;
        }
        else
        {
            m_runner = new TestRunner();
            m_ownsRunner = true;
        }
    }

    ~TestTradingPipelineExplorerCase()
    {
        if(m_ownsRunner && m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    virtual void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestGetPipelineByStage();
        TestGetPipelineByEvent();
        TestRegistryIntegration();
        TestBatchQueries();
        TestEdgeCases();
        TestComplexScenario();

        // 只有當擁有內部 TestRunner 時才顯示摘要
        if(m_ownsRunner)
        {
            m_runner.ShowSummary();
        }
    }

protected:
    // 記錄測試結果的輔助方法
    void RecordResult(TestResult* result)
    {
        m_runner.RecordResult(result);
    }

    // 測試構造函數
    void TestConstructor()
    {
        Print("=== 測試構造函數 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "測試註冊器");

        // 測試默認參數構造
        TradingPipelineExplorer* explorer1 = new TradingPipelineExplorer(registry);
        RecordResult(Assert::AssertNotNull("構造_默認參數", explorer1));
        RecordResult(Assert::AssertEquals("構造_默認名稱", "TradingPipelineExplorer", explorer1.GetName()));
        RecordResult(Assert::AssertEquals("構造_默認類型", "TradingPipelineExplorer", explorer1.GetType()));
        RecordResult(Assert::AssertTrue("構造_默認有效", explorer1.IsValid()));

        // 測試自定義參數構造
        TradingPipelineExplorer* explorer2 = new TradingPipelineExplorer(
            registry, "自定義探索器", "CustomExplorer", "自定義探索器描述");
        RecordResult(Assert::AssertEquals("構造_自定義名稱", "自定義探索器", explorer2.GetName()));
        RecordResult(Assert::AssertEquals("構造_自定義類型", "CustomExplorer", explorer2.GetType()));
        RecordResult(Assert::AssertEquals("構造_自定義描述", "自定義探索器描述", explorer2.GetDescription()));

        delete explorer1;
        delete explorer2;
        delete registry;
        delete manager;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("=== 測試基本屬性 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("屬性測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "屬性測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "屬性測試");

        // 測試基本屬性
        RecordResult(Assert::AssertEquals("屬性_名稱", "屬性測試", explorer.GetName()));
        RecordResult(Assert::AssertEquals("屬性_類型", "TradingPipelineExplorer", explorer.GetType()));
        RecordResult(Assert::AssertTrue("屬性_有效性", explorer.IsValid()));
        // 注意：GetManager 方法未定義，跳過此測試
        // RecordResult(Assert::AssertNotNull("屬性_管理器指針", explorer.GetManager()));

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試按階段獲取流水線
    void TestGetPipelineByStage()
    {
        Print("=== 測試按階段獲取流水線 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("階段測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "階段測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "階段測試");

        // 創建並註冊階段容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("初始化容器", "初始化階段容器", "Container", false, 50);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("Tick容器", "Tick階段容器", "Container", false, 50);

        registry.Register(INIT_START, initContainer);
        registry.Register(TICK_DATA_FEED, tickContainer);

        // 測試按階段查找
        ITradingPipeline* foundInit = explorer.GetPipeline(INIT_START);
        ITradingPipeline* foundTick = explorer.GetPipeline(TICK_DATA_FEED);
        ITradingPipeline* notFound = explorer.GetPipeline(DEINIT_CLEANUP);

        RecordResult(Assert::AssertNotNull("階段_找到初始化", foundInit));
        RecordResult(Assert::AssertNotNull("階段_找到Tick", foundTick));
        RecordResult(Assert::AssertNull("階段_未找到清理", notFound));

        if(foundInit != NULL)
        {
            RecordResult(Assert::AssertEquals("階段_初始化名稱", "初始化容器", foundInit.GetName()));
        }

        if(foundTick != NULL)
        {
            RecordResult(Assert::AssertEquals("階段_Tick名稱", "Tick容器", foundTick.GetName()));
        }

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試按事件獲取流水線
    void TestGetPipelineByEvent()
    {
        Print("=== 測試按事件獲取流水線 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("事件測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "事件測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "事件測試");

        // 創建並註冊事件容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("初始化事件容器", "初始化事件容器", "Container", false, 50);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("Tick事件容器", "Tick事件容器", "Container", false, 50);

        registry.Register(TRADING_INIT, initContainer);
        registry.Register(TRADING_TICK, tickContainer);

        // 測試按事件查找
        ITradingPipeline* foundInit = explorer.GetPipeline(TRADING_INIT);
        ITradingPipeline* foundTick = explorer.GetPipeline(TRADING_TICK);
        ITradingPipeline* notFound = explorer.GetPipeline(TRADING_DEINIT);

        RecordResult(Assert::AssertNotNull("事件_找到初始化", foundInit));
        RecordResult(Assert::AssertNotNull("事件_找到Tick", foundTick));
        RecordResult(Assert::AssertNull("事件_未找到清理", notFound));

        if(foundInit != NULL)
        {
            RecordResult(Assert::AssertEquals("事件_初始化名稱", "初始化事件容器", foundInit.GetName()));
        }

        if(foundTick != NULL)
        {
            RecordResult(Assert::AssertEquals("事件_Tick名稱", "Tick事件容器", foundTick.GetName()));
        }

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試與註冊器的集成
    void TestRegistryIntegration()
    {
        Print("=== 測試與註冊器的集成 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("集成測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "集成測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "集成測試");

        // 創建容器和普通流水線
        TradingPipelineContainer* container = new TradingPipelineContainer("集成測試容器", "測試容器", "Container", false, 50);
        TestExplorerTradingPipeline* pipeline = new TestExplorerTradingPipeline("集成測試流水線", "集成測試");

        // 先註冊容器，再註冊流水線到容器
        registry.Register(TRADING_INIT, container);
        manager.SetContainer(TRADING_INIT, container);
        container.AddPipeline(pipeline);

        // 通過探索器查找
        ITradingPipeline* found = explorer.GetPipeline(TRADING_INIT);
        RecordResult(Assert::AssertNotNull("集成_找到容器", found));

        if(found != NULL)
        {
            RecordResult(Assert::AssertEquals("集成_容器名稱", "集成測試容器", found.GetName()));

            // 檢查容器中的流水線
            TradingPipelineContainer* foundContainer = dynamic_cast<TradingPipelineContainer*>(found);
            if(foundContainer != NULL)
            {
                RecordResult(Assert::AssertEquals("集成_容器中流水線數量", 1, foundContainer.GetPipelineCount()));
            }
        }

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試批量查詢
    void TestBatchQueries()
    {
        Print("=== 測試批量查詢 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("批量測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "批量測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "批量測試");

        // 創建多個容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("批量初始化容器", "批量測試", "Container", false, 50);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("批量Tick容器", "批量測試", "Container", false, 50);
        TradingPipelineContainer* deinitContainer = new TradingPipelineContainer("批量清理容器", "批量測試", "Container", false, 50);

        // 註冊所有容器
        registry.Register(TRADING_INIT, initContainer);
        registry.Register(TRADING_TICK, tickContainer);
        registry.Register(TRADING_DEINIT, deinitContainer);

        // 測試批量查詢（如果探索器支持）
        RecordResult(Assert::AssertEquals("批量_註冊器事件數量", 3, registry.GetRegisteredEventCount()));
        RecordResult(Assert::AssertEquals("批量_管理器容器數量", 3, manager.GetContainerCount()));

        // 逐一驗證每個容器
        ITradingPipeline* foundInit = explorer.GetPipeline(TRADING_INIT);
        ITradingPipeline* foundTick = explorer.GetPipeline(TRADING_TICK);
        ITradingPipeline* foundDeinit = explorer.GetPipeline(TRADING_DEINIT);

        RecordResult(Assert::AssertNotNull("批量_初始化容器", foundInit));
        RecordResult(Assert::AssertNotNull("批量_Tick容器", foundTick));
        RecordResult(Assert::AssertNotNull("批量_清理容器", foundDeinit));

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("=== 測試邊界情況 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("邊界測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "邊界測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "邊界測試");

        // 測試空管理器查詢
        ITradingPipeline* notFound1 = explorer.GetPipeline(TRADING_INIT);
        ITradingPipeline* notFound2 = explorer.GetPipeline(INIT_START);
        RecordResult(Assert::AssertNull("邊界_空管理器事件查詢", notFound1));
        RecordResult(Assert::AssertNull("邊界_空管理器階段查詢", notFound2));

        // 測試 NULL 註冊器構造
        TradingPipelineExplorer* invalidExplorer = new TradingPipelineExplorer(NULL);
        RecordResult(Assert::AssertFalse("邊界_NULL註冊器無效", invalidExplorer.IsValid()));
        delete invalidExplorer;

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試複雜場景
    void TestComplexScenario()
    {
        Print("=== 測試複雜場景 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("複雜測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "複雜測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "複雜測試");

        // 創建複雜的流水線結構
        TradingPipelineContainer* mainContainer = new TradingPipelineContainer("主容器", "主要容器", "Container", false, 50);

        // 添加多個子流水線
        TestExplorerTradingPipeline* pipeline1 = new TestExplorerTradingPipeline("子流水線1", "複雜測試1");
        TestExplorerTradingPipeline* pipeline2 = new TestExplorerTradingPipeline("子流水線2", "複雜測試2");
        TestExplorerTradingPipeline* pipeline3 = new TestExplorerTradingPipeline("子流水線3", "複雜測試3");

        mainContainer.AddPipeline(pipeline1);
        mainContainer.AddPipeline(pipeline2);
        mainContainer.AddPipeline(pipeline3);

        // 註冊主容器
        registry.Register(TRADING_INIT, mainContainer);

        // 通過探索器查找並驗證
        ITradingPipeline* found = explorer.GetPipeline(TRADING_INIT);
        RecordResult(Assert::AssertNotNull("複雜_找到主容器", found));

        if(found != NULL)
        {
            TradingPipelineContainer* foundContainer = dynamic_cast<TradingPipelineContainer*>(found);
            if(foundContainer != NULL)
            {
                RecordResult(Assert::AssertEquals("複雜_主容器名稱", "主容器", foundContainer.GetName()));
                RecordResult(Assert::AssertEquals("複雜_子流水線數量", 3, foundContainer.GetPipelineCount()));
                RecordResult(Assert::AssertFalse("複雜_容器非空", foundContainer.IsEmpty()));
            }
        }

        // 測試階段映射查詢
        ITradingPipeline* foundByStage = explorer.GetPipeline(INIT_START);
        RecordResult(Assert::AssertNotNull("複雜_階段映射查詢", foundByStage));

        delete explorer;
        delete registry;
        delete manager;
    }
};

//+------------------------------------------------------------------+
//| 運行 TradingPipelineExplorer 測試的函數                        |
//+------------------------------------------------------------------+
void RunTestTradingPipelineExplorer()
{
    Print("\n🧪 開始執行 TradingPipelineExplorer 單元測試...");

    TestTradingPipelineExplorerCase* testCase = new TestTradingPipelineExplorerCase();
    testCase.RunTests();
    delete testCase;

    Print("✅ TradingPipelineExplorer 單元測試執行完成\n");
}

//+------------------------------------------------------------------+
//| 運行 TradingPipelineExplorer 測試（使用外部 TestRunner）       |
//+------------------------------------------------------------------+
void RunTestTradingPipelineExplorer(TestRunner* runner)
{
    if(runner == NULL)
    {
        RunTestTradingPipelineExplorer();
        return;
    }

    Print("\n🧪 執行 TradingPipelineExplorer 單元測試（外部 TestRunner）...");

    TestTradingPipelineExplorerCase* testCase = new TestTradingPipelineExplorerCase(runner);
    testCase.RunTests();
    delete testCase;

    Print("✅ TradingPipelineExplorer 單元測試完成\n");
}
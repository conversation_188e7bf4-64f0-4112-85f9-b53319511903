//+------------------------------------------------------------------+
//|                                            SimpleContainerTest.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單交易流水線                                           |
//+------------------------------------------------------------------+
class SimpleTestPipeline : public TradingPipeline
{
private:
    string m_testMessage;

public:
    SimpleTestPipeline(string name, string testMessage = "測試執行")
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage)
    {
    }

protected:
    virtual void Main() override
    {
        Print(StringFormat("[%s] 執行成功: %s", GetName(), m_testMessage));
    }
};

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("\n" + StringRepeat("=", 60));
    Print("  簡單容器測試");
    Print(StringRepeat("=", 60));

    // 測試 TradingPipelineContainer
    TestTradingPipelineContainer();

    Print("\n" + StringRepeat("-", 60));

    // 測試 TradingPipelineContainerManager
    TestTradingPipelineContainerManager();

    Print("\n" + StringRepeat("=", 60));
    Print("  簡單容器測試完成");
    Print(StringRepeat("=", 60));
}

//+------------------------------------------------------------------+
//| 測試 TradingPipelineContainer                                    |
//+------------------------------------------------------------------+
void TestTradingPipelineContainer()
{
    Print("=== 測試 TradingPipelineContainer ===");

    // 創建容器
    TradingPipelineContainer* container = new TradingPipelineContainer(
        "測試容器", "用於測試", "TestContainer", TRADING_TICK, false, 5);

    Print("✓ 容器創建成功");
    Print("  名稱: " + container.GetName());
    Print("  類型: " + container.GetType());
    Print("  描述: " + container.GetDescription());
    Print("  最大容量: " + IntegerToString(container.GetMaxPipelines()));
    Print("  初始數量: " + IntegerToString(container.GetPipelineCount()));
    Print("  是否為空: " + (container.IsEmpty() ? "是" : "否"));
    Print("  是否已滿: " + (container.IsFull() ? "是" : "否"));

    // 添加流水線
    SimpleTestPipeline* pipeline1 = new SimpleTestPipeline("流水線1", "測試消息1");
    SimpleTestPipeline* pipeline2 = new SimpleTestPipeline("流水線2", "測試消息2");

    bool added1 = container.AddPipeline(pipeline1);
    bool added2 = container.AddPipeline(pipeline2);

    Print("✓ 添加流水線結果:");
    Print("  流水線1: " + (added1 ? "成功" : "失敗"));
    Print("  流水線2: " + (added2 ? "成功" : "失敗"));
    Print("  當前數量: " + IntegerToString(container.GetPipelineCount()));

    // 查找流水線（FindByName 和 HasPipelineByName 方法未定義，使用 GetPipeline 替代）
    ITradingPipeline* found = container.GetPipeline(0, NULL);  // 獲取第一個流水線
    Print("✓ 查找索引0流水線: " + (found != NULL ? "找到" : "未找到"));

    // 使用流水線數量檢查替代 HasPipelineByName
    int pipelineCount = container.GetPipelineCount();
    bool hasP1 = (pipelineCount >= 1);
    bool hasP3 = (pipelineCount >= 3);
    Print("✓ 包含檢查:");
    Print("  流水線數量: " + IntegerToString(pipelineCount));
    Print("  包含流水線1: " + (hasP1 ? "是" : "否"));
    Print("  包含流水線3: " + (hasP3 ? "是" : "否"));

    // 執行容器
    Print("✓ 執行容器...");
    container.Execute();
    Print("  執行狀態: " + (container.IsExecuted() ? "已執行" : "未執行"));

    // 移除流水線（RemovePipelineByName 方法未定義，使用 RemovePipeline 替代）
    bool removed = container.RemovePipeline(pipeline1);
    Print("✓ 移除流水線1: " + (removed ? "成功" : "失敗"));
    Print("  移除後數量: " + IntegerToString(container.GetPipelineCount()));

    // 清理
    container.Clear();
    Print("✓ 清理後數量: " + IntegerToString(container.GetPipelineCount()));

    delete container;
    Print("✓ TradingPipelineContainer 測試完成");
}

//+------------------------------------------------------------------+
//| 測試 TradingPipelineContainerManager                             |
//+------------------------------------------------------------------+
void TestTradingPipelineContainerManager()
{
    Print("=== 測試 TradingPipelineContainerManager ===");

    // 創建管理器
    TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
        "測試管理器", "TestManager", false, 3);

    Print("✓ 管理器創建成功");
    Print("  名稱: " + manager.GetName());
    Print("  類型: " + manager.GetType());
    Print("  最大容量: " + IntegerToString(manager.GetMaxContainers()));
    Print("  初始數量: " + IntegerToString(manager.GetContainerCount()));

    // 創建不同事件類型的容器
    TradingPipelineContainer* initContainer = new TradingPipelineContainer(
        "初始化容器", "處理初始化", "InitContainer", TRADING_INIT);
    TradingPipelineContainer* tickContainer = new TradingPipelineContainer(
        "Tick容器", "處理Tick", "TickContainer", TRADING_TICK);
    TradingPipelineContainer* deinitContainer = new TradingPipelineContainer(
        "清理容器", "處理清理", "DeinitContainer", TRADING_DEINIT);

    // 向容器添加流水線
    SimpleTestPipeline* initPipeline = new SimpleTestPipeline("初始化流水線");
    SimpleTestPipeline* tickPipeline = new SimpleTestPipeline("Tick流水線");
    SimpleTestPipeline* deinitPipeline = new SimpleTestPipeline("清理流水線");

    initContainer.AddPipeline(initPipeline);
    tickContainer.AddPipeline(tickPipeline);
    deinitContainer.AddPipeline(deinitPipeline);

    // 添加容器到管理器（AddContainer 方法未定義，使用 SetContainer 替代）
    bool addedInit = manager.SetContainer(TRADING_INIT, initContainer);
    bool addedTick = manager.SetContainer(TRADING_TICK, tickContainer);
    bool addedDeinit = manager.SetContainer(TRADING_DEINIT, deinitContainer);

    Print("✓ 設置容器結果:");
    Print("  初始化容器: " + (addedInit ? "成功" : "失敗"));
    Print("  Tick容器: " + (addedTick ? "成功" : "失敗"));
    Print("  清理容器: " + (addedDeinit ? "成功" : "失敗"));
    Print("  當前容器數量: " + IntegerToString(manager.GetContainerCount()));

    // 按事件類型查找（FindContainerByEventType 方法未定義，使用 GetContainer 替代）
    TradingPipelineContainer* foundTick = manager.GetContainer(TRADING_TICK);
    Print("✓ 查找TICK容器: " + (foundTick != NULL ? "找到" : "未找到"));

    // 按事件類型執行
    Print("✓ 執行INIT事件...");
    manager.Execute(TRADING_INIT);
    Print("  INIT容器執行狀態: " + (initContainer.IsExecuted() ? "已執行" : "未執行"));

    Print("✓ 執行TICK事件...");
    manager.Execute(TRADING_TICK);
    Print("  TICK容器執行狀態: " + (tickContainer.IsExecuted() ? "已執行" : "未執行"));

    Print("✓ 執行DEINIT事件...");
    manager.Execute(TRADING_DEINIT);
    Print("  DEINIT容器執行狀態: " + (deinitContainer.IsExecuted() ? "已執行" : "未執行"));

    // 執行所有容器（RestoreAll 和 ExecuteAll 方法未定義，分別處理各事件類型）
    manager.Restore(TRADING_INIT);
    manager.Restore(TRADING_TICK);
    manager.Restore(TRADING_DEINIT);
    Print("✓ 重置所有容器");

    manager.Execute(TRADING_INIT);
    manager.Execute(TRADING_TICK);
    manager.Execute(TRADING_DEINIT);
    Print("✓ 執行所有容器");
    Print("  管理器執行狀態: " + (manager.IsExecuted() ? "已執行" : "未執行"));

    delete manager;
    Print("✓ TradingPipelineContainerManager 測試完成");
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}

//+------------------------------------------------------------------+
//|                                    TestTradingPipelineRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineRegistry.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單交易流水線                                           |
//+------------------------------------------------------------------+
class TestRegistryTradingPipeline : public TradingPipeline
{
private:
    string m_testMessage;
    bool m_shouldFail;

public:
    TestRegistryTradingPipeline(string name, string testMessage = "測試執行", bool shouldFail = false)
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage),
          m_shouldFail(shouldFail)
    {
    }

protected:
    virtual void Main() override
    {
        if(m_shouldFail)
        {
            Print(StringFormat("[%s] 模擬執行失敗: %s", GetName(), m_testMessage));
        }
        else
        {
            Print(StringFormat("[%s] 執行成功: %s", GetName(), m_testMessage));
        }
    }
};

//+------------------------------------------------------------------+
//| TradingPipelineRegistry 測試類                                  |
//+------------------------------------------------------------------+
class TestTradingPipelineRegistryCase : public TestCase
{
private:
    TestRunner* m_runner;
    bool m_ownsRunner;

public:
    // 構造函數 - 可以接受外部 TestRunner 或創建內部 TestRunner
    TestTradingPipelineRegistryCase(TestRunner* externalRunner = NULL) : TestCase("TestTradingPipelineRegistry")
    {
        if(externalRunner != NULL)
        {
            m_runner = externalRunner;
            m_ownsRunner = false;
        }
        else
        {
            m_runner = new TestRunner();
            m_ownsRunner = true;
        }
    }

    ~TestTradingPipelineRegistryCase()
    {
        if(m_ownsRunner && m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    virtual void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestRegisterStage();
        TestRegisterEvent();
        TestUnregisterStage();
        TestUnregisterEvent();
        TestContainerRegistration();
        TestPipelineRegistration();
        TestMaxRegistrationsLimit();
        TestOwnershipManagement();
        TestEdgeCases();

        // 只有當擁有內部 TestRunner 時才顯示摘要
        if(m_ownsRunner)
        {
            m_runner.ShowSummary();
        }
    }

protected:
    // 記錄測試結果的輔助方法
    void RecordResult(TestResult* result)
    {
        m_runner.RecordResult(result);
    }

    // 測試構造函數
    void TestConstructor()
    {
        Print("=== 測試構造函數 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("測試管理器");

        // 測試默認參數構造
        TradingPipelineRegistry* registry1 = new TradingPipelineRegistry(manager);
        RecordResult(Assert::AssertNotNull("構造_默認參數", registry1));
        RecordResult(Assert::AssertEquals("構造_默認名稱", "TradingPipelineRegistry", registry1.GetName()));
        RecordResult(Assert::AssertEquals("構造_默認類型", "PipelineRegistry", registry1.GetType()));
        RecordResult(Assert::AssertEquals("構造_默認最大註冊數", 50, registry1.GetMaxRegistrations()));
        RecordResult(Assert::AssertTrue("構造_默認啟用", registry1.IsEnabled()));

        // 測試自定義參數構造
        TradingPipelineRegistry* registry2 = new TradingPipelineRegistry(
            manager, "自定義註冊器", "CustomRegistry", 20, true);
        RecordResult(Assert::AssertEquals("構造_自定義名稱", "自定義註冊器", registry2.GetName()));
        RecordResult(Assert::AssertEquals("構造_自定義類型", "CustomRegistry", registry2.GetType()));
        RecordResult(Assert::AssertEquals("構造_自定義最大註冊數", 20, registry2.GetMaxRegistrations()));

        delete registry1;
        delete registry2;
        delete manager;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("=== 測試基本屬性 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("屬性測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "屬性測試");

        // 測試初始狀態
        RecordResult(Assert::AssertEquals("屬性_初始階段註冊數", 0, registry.GetRegisteredStageCount()));
        RecordResult(Assert::AssertEquals("屬性_初始事件註冊數", 0, registry.GetRegisteredEventCount()));
        RecordResult(Assert::AssertEquals("屬性_初始總註冊數", 0, registry.GetTotalRegistrations()));
        RecordResult(Assert::AssertFalse("屬性_初始未滿", registry.IsFull()));
        // 注意：HasEmptySlot 方法未定義，跳過此測試
        // RecordResult(Assert::AssertTrue("屬性_初始有空位", registry.HasEmptySlot()));

        // 測試啟用/禁用
        registry.SetEnabled(false);
        RecordResult(Assert::AssertFalse("屬性_禁用後狀態", registry.IsEnabled()));
        registry.SetEnabled(true);
        RecordResult(Assert::AssertTrue("屬性_重新啟用後狀態", registry.IsEnabled()));

        delete registry;
        delete manager;
    }

    // 測試註冊階段
    void TestRegisterStage()
    {
        Print("=== 測試註冊階段 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("階段測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "階段測試");

        // 創建測試容器
        TradingPipelineContainer* container = new TradingPipelineContainer("階段測試容器", "測試容器", "Container");

        // 測試註冊階段
        bool result = registry.Register(INIT_START, container);
        RecordResult(Assert::AssertTrue("階段_註冊成功", result));
        RecordResult(Assert::AssertTrue("階段_已註冊檢查", registry.IsStageRegistered(INIT_START)));
        RecordResult(Assert::AssertEquals("階段_註冊後計數", 1, registry.GetRegisteredStageCount()));

        // 測試獲取已註冊的階段容器
        TradingPipelineContainer* retrieved = registry.GetRegisteredStageContainer(INIT_START);
        RecordResult(Assert::AssertNotNull("階段_獲取已註冊容器", retrieved));
        RecordResult(Assert::AssertEquals("階段_獲取容器名稱", "階段測試容器", retrieved.GetName()));

        delete registry;
        delete manager;
    }

    // 測試註冊事件
    void TestRegisterEvent()
    {
        Print("=== 測試註冊事件 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("事件測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "事件測試");

        // 創建測試容器
        TradingPipelineContainer* container = new TradingPipelineContainer("事件測試容器", "測試容器", "Container");

        // 測試註冊事件
        bool result = registry.Register(TRADING_TICK, container);
        RecordResult(Assert::AssertTrue("事件_註冊成功", result));
        RecordResult(Assert::AssertTrue("事件_已註冊檢查", registry.IsEventRegistered(TRADING_TICK)));
        RecordResult(Assert::AssertEquals("事件_註冊後計數", 1, registry.GetRegisteredEventCount()));

        // 測試獲取已註冊的事件容器
        TradingPipelineContainer* retrieved = registry.GetRegisteredEventContainer(TRADING_TICK);
        RecordResult(Assert::AssertNotNull("事件_獲取已註冊容器", retrieved));
        RecordResult(Assert::AssertEquals("事件_獲取容器名稱", "事件測試容器", retrieved.GetName()));

        delete registry;
        delete manager;
    }

    // 測試取消註冊階段
    void TestUnregisterStage()
    {
        Print("=== 測試取消註冊階段 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("取消階段測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "取消階段測試");

        // 先註冊一個階段
        TradingPipelineContainer* container = new TradingPipelineContainer("取消測試容器", "測試容器", "Container", false, 50);
        registry.Register(INIT_START, container);

        // 測試取消註冊
        bool result = registry.UnregisterStage(INIT_START);
        RecordResult(Assert::AssertTrue("取消階段_成功", result));
        RecordResult(Assert::AssertFalse("取消階段_已移除檢查", registry.IsStageRegistered(INIT_START)));
        RecordResult(Assert::AssertEquals("取消階段_計數減少", 0, registry.GetRegisteredStageCount()));

        // 測試取消不存在的階段
        bool result2 = registry.UnregisterStage(TICK_DATA_FEED);
        RecordResult(Assert::AssertFalse("取消階段_不存在階段", result2));

        delete registry;
        delete manager;
    }

    // 測試取消註冊事件
    void TestUnregisterEvent()
    {
        Print("=== 測試取消註冊事件 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("取消事件測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "取消事件測試");

        // 先註冊一個事件
        TradingPipelineContainer* container = new TradingPipelineContainer("取消測試容器", "測試容器", "Container", false, 50);
        registry.Register(TRADING_TICK, container);

        // 測試取消註冊
        bool result = registry.UnregisterEvent(TRADING_TICK);
        RecordResult(Assert::AssertTrue("取消事件_成功", result));
        RecordResult(Assert::AssertFalse("取消事件_已移除檢查", registry.IsEventRegistered(TRADING_TICK)));
        RecordResult(Assert::AssertEquals("取消事件_計數減少", 0, registry.GetRegisteredEventCount()));

        // 測試取消不存在的事件
        bool result2 = registry.UnregisterEvent(TRADING_DEINIT);
        RecordResult(Assert::AssertFalse("取消事件_不存在事件", result2));

        delete registry;
        delete manager;
    }

    // 測試容器註冊
    void TestContainerRegistration()
    {
        Print("=== 測試容器註冊 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("容器註冊測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "容器註冊測試");

        // 創建容器類型的流水線
        TradingPipelineContainer* container = new TradingPipelineContainer("容器流水線", "測試容器", "Container", false, 50);

        // 測試容器註冊 - 應該直接設置到管理器
        bool result = registry.Register(TRADING_INIT, container);
        RecordResult(Assert::AssertTrue("容器註冊_成功", result));

        // 檢查管理器中是否有容器
        TradingPipelineContainer* managerContainer = manager.GetContainer(TRADING_INIT);
        RecordResult(Assert::AssertNotNull("容器註冊_管理器中存在", managerContainer));
        RecordResult(Assert::AssertEquals("容器註冊_名稱匹配", "容器流水線", managerContainer.GetName()));

        delete registry;
        delete manager;
    }

    // 測試普通流水線註冊
    void TestPipelineRegistration()
    {
        Print("=== 測試普通流水線註冊 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("流水線註冊測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "流水線註冊測試");

        // 先創建一個容器並設置到管理器
        TradingPipelineContainer* container = new TradingPipelineContainer("基礎容器", "測試容器", "Container", false, 50);
        manager.SetContainer(TRADING_INIT, container);

        // 創建普通流水線
        TestRegistryTradingPipeline* pipeline = new TestRegistryTradingPipeline("普通流水線", "測試普通流水線");

        // 注意：Register 方法參數不匹配，修正調用
        bool result = registry.Register(pipeline);
        RecordResult(Assert::AssertTrue("流水線註冊_成功", result));

        // 檢查容器中是否有流水線
        RecordResult(Assert::AssertEquals("流水線註冊_容器中流水線數量", 1, container.GetPipelineCount()));

        delete registry;
        delete manager;
    }

    // 測試最大註冊數限制
    void TestMaxRegistrationsLimit()
    {
        Print("=== 測試最大註冊數限制 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("限制測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "限制測試", "LimitTest", 2);

        RecordResult(Assert::AssertEquals("限制_最大註冊數", 2, registry.GetMaxRegistrations()));

        // 註冊到最大數量
        TradingPipelineContainer* container1 = new TradingPipelineContainer("限制測試容器1", "測試容器1", "Container", false, 50);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("限制測試容器2", "測試容器2", "Container", false, 50);
        TradingPipelineContainer* container3 = new TradingPipelineContainer("限制測試容器3", "測試容器3", "Container", false, 50);

        RecordResult(Assert::AssertTrue("限制_註冊第1個", registry.Register(TRADING_INIT, container1)));
        RecordResult(Assert::AssertTrue("限制_註冊第2個", registry.Register(TRADING_TICK, container2)));
        RecordResult(Assert::AssertTrue("限制_已滿", registry.IsFull()));
        // 注意：HasEmptySlot 方法未定義，跳過此測試
        // RecordResult(Assert::AssertFalse("限制_無空位", registry.HasEmptySlot()));

        // 嘗試超過限制
        RecordResult(Assert::AssertFalse("限制_超過限制失敗", registry.Register(TRADING_DEINIT, container3)));

        delete container3; // 未註冊的容器需要手動刪除
        delete registry;
        delete manager;
    }

    // 測試擁有權管理
    void TestOwnershipManagement()
    {
        Print("=== 測試擁有權管理 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("擁有權測試管理器");

        // 測試 owned = true（默認）
        TradingPipelineRegistry* registry1 = new TradingPipelineRegistry(manager, "擁有權測試1", "OwnershipTest", 50, true);
        RecordResult(Assert::AssertTrue("擁有權_默認擁有", registry1.IsOwned()));

        // 測試 owned = false
        TradingPipelineRegistry* registry2 = new TradingPipelineRegistry(manager, "擁有權測試2", "OwnershipTest", 50, false);
        RecordResult(Assert::AssertFalse("擁有權_不擁有", registry2.IsOwned()));

        delete registry1;
        delete registry2;
        delete manager;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("=== 測試邊界情況 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("邊界測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "邊界測試");

        // 測試註冊 NULL 流水線
        bool result1 = registry.Register(TRADING_INIT, NULL);
        RecordResult(Assert::AssertFalse("邊界_NULL流水線註冊失敗", result1));

        // 測試禁用狀態下的註冊
        registry.SetEnabled(false);
        TradingPipelineContainer* container = new TradingPipelineContainer("邊界測試容器", "測試容器", "Container", false, 50);
        bool result2 = registry.Register(TRADING_INIT, container);
        RecordResult(Assert::AssertFalse("邊界_禁用時註冊失敗", result2));

        // 重新啟用並測試
        registry.SetEnabled(true);
        bool result3 = registry.Register(TRADING_INIT, container);
        RecordResult(Assert::AssertTrue("邊界_啟用後註冊成功", result3));

        // 測試重複註冊同一階段
        TradingPipelineContainer* container2 = new TradingPipelineContainer("重複測試容器", "測試容器", "Container", false, 50);
        bool result4 = registry.Register(TRADING_INIT, container2);
        RecordResult(Assert::AssertFalse("邊界_重複註冊失敗", result4));

        delete container2; // 未註冊的容器需要手動刪除
        delete registry;
        delete manager;
    }
};

//+------------------------------------------------------------------+
//| 運行 TradingPipelineRegistry 測試的函數                        |
//+------------------------------------------------------------------+
void RunTestTradingPipelineRegistry()
{
    Print("\n🧪 開始執行 TradingPipelineRegistry 單元測試...");

    TestTradingPipelineRegistryCase* testCase = new TestTradingPipelineRegistryCase();
    testCase.RunTests();
    delete testCase;

    Print("✅ TradingPipelineRegistry 單元測試執行完成\n");
}

//+------------------------------------------------------------------+
//| 運行 TradingPipelineRegistry 測試（使用外部 TestRunner）       |
//+------------------------------------------------------------------+
void RunTestTradingPipelineRegistry(TestRunner* runner)
{
    if(runner == NULL)
    {
        RunTestTradingPipelineRegistry();
        return;
    }

    Print("\n🧪 執行 TradingPipelineRegistry 單元測試（外部 TestRunner）...");

    TestTradingPipelineRegistryCase* testCase = new TestTradingPipelineRegistryCase(runner);
    testCase.RunTests();
    delete testCase;

    Print("✅ TradingPipelineRegistry 單元測試完成\n");
}
//+------------------------------------------------------------------+
//|                                              SimpleTestRunner_v2.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipeline.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "MockTradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 簡化版整合測試運行器 v2                                           |
//+------------------------------------------------------------------+
class SimpleIntegrationTestRunner_v2 : public TestRunner
{
public:
    // 構造函數
    SimpleIntegrationTestRunner_v2() : TestRunner() {}

    // 運行整合測試
    void RunIntegrationTests()
    {
        Print("🚀 開始執行 PipelineAdvance_v1 整合測試 v2...");

        // 運行基本工作流程測試
        TestBasicWorkflow();

        // 運行 PipelineGroupManager 測試
        TestPipelineGroupManager();

        // 顯示摘要
        ShowSummary();

        Print("✅ PipelineAdvance_v1 整合測試執行完成");
    }

private:
    // 測試基本工作流程
    void TestBasicWorkflow()
    {
        Print("=== 測試基本工作流程 ===");

        // 創建基本流水線
        MockTradingPipeline* dataFeed = MockTradingPipelineFactory::CreateDataFeedPipeline();
        MockTradingPipeline* signal = MockTradingPipelineFactory::CreateSignalPipeline();

        // 創建複合流水線
        CompositePipeline* mainPipeline = new CompositePipeline("主工作流程");
        mainPipeline.AddPipeline(dataFeed);
        mainPipeline.AddPipeline(signal);

        // 創建流水線組
        PipelineGroup* tickGroup = new PipelineGroup("Tick處理組", "處理每個Tick的流水線組", TRADING_TICK);
        tickGroup.AddPipeline(mainPipeline);

        // 執行工作流程
        tickGroup.ExecuteAll();

        // 驗證結果
        RecordResult(Assert::AssertTrue("基本工作流程_組執行成功", tickGroup.IsExecuted()));
        RecordResult(Assert::AssertTrue("基本工作流程_主流水線執行", mainPipeline.IsExecuted()));

        // 清理
        delete tickGroup;
        delete mainPipeline;
        delete dataFeed;
        delete signal;
    }

    // 測試 PipelineGroupManager
    void TestPipelineGroupManager()
    {
        Print("=== 測試 PipelineGroupManager ===");

        // 創建管理器
        PipelineGroupManager* manager = new PipelineGroupManager("整合測試管理器");

        // 驗證基本屬性
        RecordResult(Assert::AssertEquals("管理器_名稱", "整合測試管理器", manager.GetName()));
        RecordResult(Assert::AssertEquals("管理器_類型", "PipelineGroupManager", manager.GetType()));
        RecordResult(Assert::AssertEquals("管理器_最大組數", 3, manager.GetMaxGroups()));
        RecordResult(Assert::AssertEquals("管理器_初始組數", 0, manager.GetGroupCount()));

        // 創建並添加流水線組
        PipelineGroup* group1 = new PipelineGroup("測試組1", "第一個測試組", TRADING_INIT);
        PipelineGroup* group2 = new PipelineGroup("測試組2", "第二個測試組", TRADING_TICK);

        RecordResult(Assert::AssertTrue("管理器_添加組1", manager.AddGroup(group1)));
        RecordResult(Assert::AssertTrue("管理器_添加組2", manager.AddGroup(group2)));
        RecordResult(Assert::AssertEquals("管理器_添加後組數", 2, manager.GetGroupCount()));

        // 測試事件驅動執行
        TestEventDrivenExecution(manager, group1, group2);

        // 清理
        delete manager;
        delete group1;
        delete group2;
    }

    // 測試事件驅動執行
    void TestEventDrivenExecution(PipelineGroupManager* manager, PipelineGroup* initGroup, PipelineGroup* tickGroup)
    {
        Print("--- 測試事件驅動執行 ---");

        // 為組添加流水線
        MockTradingPipeline* initPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("初始化流水線");
        MockTradingPipeline* tickPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("Tick流水線");

        CompositePipeline* initComposite = new CompositePipeline("初始化複合");
        CompositePipeline* tickComposite = new CompositePipeline("Tick複合");

        initComposite.AddPipeline(initPipeline);
        tickComposite.AddPipeline(tickPipeline);

        initGroup.AddPipeline(initComposite);
        tickGroup.AddPipeline(tickComposite);

        // 測試 TRADING_INIT 事件執行
        manager.Execute(TRADING_INIT);
        RecordResult(Assert::AssertTrue("事件執行_INIT組執行", initGroup.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件執行_TICK組未執行", tickGroup.IsExecuted()));

        // 重置管理器狀態以便下次執行
        manager.RestoreAll();

        // 測試 TRADING_TICK 事件執行
        manager.Execute(TRADING_TICK);
        RecordResult(Assert::AssertFalse("事件執行_INIT組重置後未執行", initGroup.IsExecuted()));
        RecordResult(Assert::AssertTrue("事件執行_TICK組執行", tickGroup.IsExecuted()));

        // 清理
        delete initComposite;
        delete tickComposite;
        delete initPipeline;
        delete tickPipeline;
    }
};

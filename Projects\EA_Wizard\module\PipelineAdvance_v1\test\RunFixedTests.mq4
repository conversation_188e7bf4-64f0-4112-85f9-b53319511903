//+------------------------------------------------------------------+
//|                                                RunFixedTests.mq4 |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 只包含修正後的測試文件
#include "TestFramework.mqh"
#include "unit/TestTradingPipelineContainer.mqh"
#include "unit/TestTradingPipelineContainerManager_Fixed.mqh"
#include "unit/TestTradingPipelineRegistry.mqh"
#include "unit/TestTradingPipelineExplorer.mqh"
#include "unit/TestEventPipeline.mqh"
#include "unit/TestStagePipeline.mqh"
#include "integration/TestPipelineAdvanceV1_Complete.mqh"

//+------------------------------------------------------------------+
//| 運行修正後的測試套件                                             |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 開始執行 PipelineAdvance_v1 修正後的測試套件");
    Print("=" + StringRepeat("=", 60));
    Print("測試時間: " + TimeToString(TimeCurrent()));
    Print("=" + StringRepeat("=", 60));

    // 創建統一的測試運行器
    TestRunner* mainRunner = new TestRunner();

    Print("\n📋 執行修正後的單元測試");
    Print(StringRepeat("-", 40));

    // 1. TradingPipelineContainer 測試
    Print("\n🧪 TradingPipelineContainer 測試");
    TestTradingPipelineContainerCase* containerTest = new TestTradingPipelineContainerCase(mainRunner);
    containerTest.RunTests();
    delete containerTest;

    // 2. TradingPipelineContainerManager 測試（修正版）
    Print("\n🧪 TradingPipelineContainerManager 測試（修正版）");
    TestTradingPipelineContainerManagerFixed* managerTest = new TestTradingPipelineContainerManagerFixed(mainRunner);
    managerTest.RunTests();
    delete managerTest;

    // 3. TradingPipelineRegistry 測試
    Print("\n🧪 TradingPipelineRegistry 測試");
    TestTradingPipelineRegistryCase* registryTest = new TestTradingPipelineRegistryCase(mainRunner);
    registryTest.RunTests();
    delete registryTest;

    // 4. TradingPipelineExplorer 測試
    Print("\n🧪 TradingPipelineExplorer 測試");
    TestTradingPipelineExplorerCase* explorerTest = new TestTradingPipelineExplorerCase(mainRunner);
    explorerTest.RunTests();
    delete explorerTest;

    // 5. EventPipeline 測試
    Print("\n🧪 EventPipeline 測試");
    TestEventPipelineCase* eventTest = new TestEventPipelineCase(mainRunner);
    eventTest.RunTests();
    delete eventTest;

    // 6. StagePipeline 測試
    Print("\n🧪 StagePipeline 測試");
    TestStagePipelineCase* stageTest = new TestStagePipelineCase(mainRunner);
    stageTest.RunTests();
    delete stageTest;

    Print("\n📋 執行整合測試");
    Print(StringRepeat("-", 40));

    // 7. 完整整合測試
    Print("\n🧪 完整模組整合測試");
    TestPipelineAdvanceV1CompleteCase* completeTest = new TestPipelineAdvanceV1CompleteCase(mainRunner);
    completeTest.RunTests();
    delete completeTest;

    Print("\n" + StringRepeat("=", 60));
    Print("📊 測試結果摘要");
    Print(StringRepeat("=", 60));

    // 顯示最終摘要
    mainRunner.ShowSummary();

    delete mainRunner;

    Print("\n" + StringRepeat("=", 60));
    Print("✅ PipelineAdvance_v1 修正後的測試套件執行完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print("=" + StringRepeat("=", 60));

    // 提供使用建議
    Print("\n💡 測試結果說明:");
    Print("- 如果所有測試都通過，說明模組 API 修正成功");
    Print("- 如果仍有錯誤，請檢查控制台輸出的詳細錯誤信息");
    Print("- 建議在修正所有錯誤後再運行完整的測試套件");
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                   |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}

//+------------------------------------------------------------------+
//| 運行特定測試的函數                                               |
//+------------------------------------------------------------------+
void RunSpecificTest(string testName)
{
    Print("🎯 運行特定測試: " + testName);
    
    TestRunner* runner = new TestRunner();
    
    if(testName == "Container")
    {
        TestTradingPipelineContainerCase* test = new TestTradingPipelineContainerCase(runner);
        test.RunTests();
        delete test;
    }
    else if(testName == "Manager")
    {
        TestTradingPipelineContainerManagerFixed* test = new TestTradingPipelineContainerManagerFixed(runner);
        test.RunTests();
        delete test;
    }
    else if(testName == "Registry")
    {
        TestTradingPipelineRegistryCase* test = new TestTradingPipelineRegistryCase(runner);
        test.RunTests();
        delete test;
    }
    else if(testName == "Explorer")
    {
        TestTradingPipelineExplorerCase* test = new TestTradingPipelineExplorerCase(runner);
        test.RunTests();
        delete test;
    }
    else if(testName == "Event")
    {
        TestEventPipelineCase* test = new TestEventPipelineCase(runner);
        test.RunTests();
        delete test;
    }
    else if(testName == "Stage")
    {
        TestStagePipelineCase* test = new TestStagePipelineCase(runner);
        test.RunTests();
        delete test;
    }
    else if(testName == "Complete")
    {
        TestPipelineAdvanceV1CompleteCase* test = new TestPipelineAdvanceV1CompleteCase(runner);
        test.RunTests();
        delete test;
    }
    else
    {
        Print("❌ 未知的測試名稱: " + testName);
        Print("可用的測試: Container, Manager, Registry, Explorer, Event, Stage, Complete");
    }
    
    runner.ShowSummary();
    delete runner;
}

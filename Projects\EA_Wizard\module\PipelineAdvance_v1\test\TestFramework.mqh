//+------------------------------------------------------------------+
//|                                              TestFramework.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

//+------------------------------------------------------------------+
//| 測試結果類別 - 記錄單個測試的結果                                 |
//+------------------------------------------------------------------+
class TestResult
{
private:
    bool m_passed;           // 測試是否通過
    string m_testName;       // 測試名稱
    string m_message;        // 測試消息

public:
    // 構造函數
    TestResult(string testName, bool passed, string message = "")
        : m_testName(testName), m_passed(passed), m_message(message) {}

    // 獲取測試名稱
    string GetTestName() const { return m_testName; }

    // 檢查測試是否通過
    bool IsPassed() const { return m_passed; }

    // 獲取測試消息
    string GetMessage() const { return m_message; }
};

//+------------------------------------------------------------------+
//| 斷言類別 - 提供各種測試斷言方法                                   |
//+------------------------------------------------------------------+
class Assert
{
public:
    // 斷言為真
    static TestResult* AssertTrue(string testName, bool condition, string message = "")
    {
        if(!condition && message == "")
            message = "期望條件為真，但實際為假";
        return new TestResult(testName, condition, message);
    }

    // 斷言為假
    static TestResult* AssertFalse(string testName, bool condition, string message = "")
    {
        if(condition && message == "")
            message = "期望條件為假，但實際為真";
        return new TestResult(testName, !condition, message);
    }

    // 斷言相等（字符串）
    static TestResult* AssertEquals(string testName, string expected, string actual, string message = "")
    {
        bool isEqual = (expected == actual);
        if(!isEqual && message == "")
            message = StringFormat("期望值: '%s', 實際值: '%s'", expected, actual);
        return new TestResult(testName, isEqual, message);
    }

    // 斷言相等（整數）
    static TestResult* AssertEquals(string testName, int expected, int actual, string message = "")
    {
        bool isEqual = (expected == actual);
        if(!isEqual && message == "")
            message = StringFormat("期望值: %d, 實際值: %d", expected, actual);
        return new TestResult(testName, isEqual, message);
    }

    // 斷言相等（布爾）
    static TestResult* AssertEquals(string testName, bool expected, bool actual, string message = "")
    {
        bool isEqual = (expected == actual);
        if(!isEqual && message == "")
            message = StringFormat("期望值: %s, 實際值: %s",
                                 expected ? "true" : "false",
                                 actual ? "true" : "false");
        return new TestResult(testName, isEqual, message);
    }

    // 斷言不為空
    static TestResult* AssertNotNull(string testName, void* pointer, string message = "")
    {
        bool isNotNull = (pointer != NULL);
        if(!isNotNull && message == "")
            message = "期望指針不為空，但實際為空";
        return new TestResult(testName, isNotNull, message);
    }

    // 斷言為空
    static TestResult* AssertNull(string testName, void* pointer, string message = "")
    {
        bool isNull = (pointer == NULL);
        if(!isNull && message == "")
            message = "期望指針為空，但實際不為空";
        return new TestResult(testName, isNull, message);
    }

    // 斷言包含字符串
    static TestResult* AssertContains(string testName, string haystack, string needle, string message = "")
    {
        bool contains = (StringFind(haystack, needle) >= 0);
        if(!contains && message == "")
            message = StringFormat("期望 '%s' 包含 '%s'", haystack, needle);
        return new TestResult(testName, contains, message);
    }

    // 斷言不包含字符串
    static TestResult* AssertNotContains(string testName, string haystack, string needle, string message = "")
    {
        bool notContains = (StringFind(haystack, needle) < 0);
        if(!notContains && message == "")
            message = StringFormat("期望 '%s' 不包含 '%s'", haystack, needle);
        return new TestResult(testName, notContains, message);
    }
};

//+------------------------------------------------------------------+
//| 測試基類 - 所有測試類別的基類                                     |
//+------------------------------------------------------------------+
class TestCase
{
protected:
    string m_testClassName;  // 測試類別名稱

public:
    // 構造函數
    TestCase(string className) : m_testClassName(className) {}

    // 虛析構函數
    virtual ~TestCase() {}

    // 獲取測試類別名稱
    string GetClassName() const { return m_testClassName; }

    // 運行所有測試（由子類實現）
    virtual void RunTests() = 0;

    // 設置方法（在每個測試前調用）
    virtual void SetUp() {}

    // 清理方法（在每個測試後調用）
    virtual void TearDown() {}
};

//+------------------------------------------------------------------+
//| 性能測試結果類別                                                 |
//+------------------------------------------------------------------+
class PerformanceResult
{
private:
    string m_testName;
    ulong m_startTime;
    ulong m_endTime;
    ulong m_duration;

public:
    PerformanceResult(string testName, ulong startTime, ulong endTime)
        : m_testName(testName), m_startTime(startTime), m_endTime(endTime)
    {
        m_duration = endTime - startTime;
    }

    string GetTestName() const { return m_testName; }
    ulong GetDuration() const { return m_duration; }
    double GetDurationMs() const { return (double)m_duration / 1000.0; }
};

//+------------------------------------------------------------------+
//| 測試文檔生成器                                                   |
//+------------------------------------------------------------------+
class TestDocumentGenerator
{
private:
    string m_outputPath;
    bool m_enabled;

public:
    TestDocumentGenerator(string outputPath = "test_results.txt", bool enabled = true)
        : m_outputPath(outputPath), m_enabled(enabled) {}

    void SetEnabled(bool enabled) { m_enabled = enabled; }
    void SetOutputPath(string path) { m_outputPath = path; }

    void WriteTestSummary(int total, int passed, int failed, double successRate)
    {
        if(!m_enabled) return;

        int fileHandle = FileOpen(m_outputPath, FILE_WRITE|FILE_TXT);
        if(fileHandle != INVALID_HANDLE)
        {
            FileWrite(fileHandle, "=== PipelineAdvance_v1 測試結果摘要 ===");
            FileWrite(fileHandle, "生成時間: " + TimeToString(TimeCurrent()));
            FileWrite(fileHandle, "總測試數: " + IntegerToString(total));
            FileWrite(fileHandle, "通過測試: " + IntegerToString(passed));
            FileWrite(fileHandle, "失敗測試: " + IntegerToString(failed));
            FileWrite(fileHandle, "成功率: " + DoubleToString(successRate, 2) + "%");
            FileWrite(fileHandle, "");
            FileClose(fileHandle);
        }
    }

    void AppendTestResult(string testName, bool passed, string message = "")
    {
        if(!m_enabled) return;

        int fileHandle = FileOpen(m_outputPath, FILE_READ|FILE_WRITE|FILE_TXT);
        if(fileHandle != INVALID_HANDLE)
        {
            FileSeek(fileHandle, 0, SEEK_END);
            string status = passed ? "[通過]" : "[失敗]";
            string line = status + " " + testName;
            if(message != "") line += ": " + message;
            FileWrite(fileHandle, line);
            FileClose(fileHandle);
        }
    }
};

//+------------------------------------------------------------------+
//| 增強版測試運行器 - 管理和執行所有測試                             |
//+------------------------------------------------------------------+
class TestRunner
{
private:
    int m_totalTests;        // 總測試數
    int m_passedTests;       // 通過的測試數
    int m_failedTests;       // 失敗的測試數
    int m_displayLimit;      // 顯示限制
    bool m_showAllResults;   // 是否顯示所有結果
    TestDocumentGenerator* m_docGenerator; // 文檔生成器
    PerformanceResult* m_performanceResults[]; // 性能測試結果
    bool m_performanceEnabled; // 是否啟用性能測試

public:
    // 構造函數
    TestRunner(int displayLimit = 10, bool enableDocuments = true)
        : m_totalTests(0), m_passedTests(0), m_failedTests(0),
          m_displayLimit(displayLimit), m_showAllResults(displayLimit <= 0),
          m_performanceEnabled(false)
    {
        m_docGenerator = new TestDocumentGenerator("PipelineAdvance_v1_test_results.txt", enableDocuments);
        ArrayResize(m_performanceResults, 0);
    }

    ~TestRunner()
    {
        if(m_docGenerator != NULL)
        {
            delete m_docGenerator;
            m_docGenerator = NULL;
        }
        CleanupPerformanceResults();
    }

    // 設置顯示限制
    void SetDisplayLimit(int limit)
    {
        m_displayLimit = limit;
        m_showAllResults = (limit <= 0);
    }

    // 啟用/禁用性能測試
    void SetPerformanceEnabled(bool enabled) { m_performanceEnabled = enabled; }

    // 啟用/禁用文檔生成
    void SetDocumentationEnabled(bool enabled)
    {
        if(m_docGenerator != NULL) m_docGenerator.SetEnabled(enabled);
    }

    // 記錄測試結果
    virtual void RecordResult(TestResult* result)
    {
        if(result == NULL) return;

        m_totalTests++;
        bool shouldDisplay = m_showAllResults || m_totalTests <= m_displayLimit;

        if(result.IsPassed())
        {
            m_passedTests++;
            if(shouldDisplay)
                Print(StringFormat("[通過] %s", result.GetTestName()));
        }
        else
        {
            m_failedTests++;
            if(shouldDisplay)
                Print(StringFormat("[失敗] %s: %s", result.GetTestName(), result.GetMessage()));
        }

        // 記錄到文檔
        if(m_docGenerator != NULL)
        {
            m_docGenerator.AppendTestResult(result.GetTestName(), result.IsPassed(), result.GetMessage());
        }

        delete result;
    }

    // 開始性能測試
    ulong StartPerformanceTest(string testName)
    {
        if(!m_performanceEnabled) return 0;
        return GetMicrosecondCount();
    }

    // 結束性能測試
    void EndPerformanceTest(string testName, ulong startTime)
    {
        if(!m_performanceEnabled || startTime == 0) return;

        ulong endTime = GetMicrosecondCount();
        PerformanceResult* result = new PerformanceResult(testName, startTime, endTime);

        int size = ArraySize(m_performanceResults);
        ArrayResize(m_performanceResults, size + 1);
        m_performanceResults[size] = result;
    }

    // 運行測試類別
    virtual void RunTestCase(TestCase* testCase)
    {
        if(testCase == NULL) return;

        Print(StringFormat("=== 開始執行測試類別: %s ===", testCase.GetClassName()));

        ulong startTime = StartPerformanceTest(testCase.GetClassName());
        testCase.RunTests();
        EndPerformanceTest(testCase.GetClassName(), startTime);

        Print(StringFormat("=== 完成測試類別: %s ===", testCase.GetClassName()));
    }

    // 顯示測試摘要
    virtual void ShowSummary()
    {
        double successRate = m_totalTests > 0 ? (double)m_passedTests / m_totalTests * 100.0 : 0.0;

        Print("=== 測試摘要 ===");
        Print(StringFormat("總測試數: %d", m_totalTests));
        Print(StringFormat("通過: %d", m_passedTests));
        Print(StringFormat("失敗: %d", m_failedTests));
        Print(StringFormat("成功率: %.2f%%", successRate));

        if(!m_showAllResults && m_totalTests > m_displayLimit)
        {
            Print(StringFormat("注意: 僅顯示前 %d 個測試結果，完整結果請查看文檔", m_displayLimit));
        }

        // 顯示性能結果
        ShowPerformanceResults();

        // 生成文檔摘要
        if(m_docGenerator != NULL)
        {
            m_docGenerator.WriteTestSummary(m_totalTests, m_passedTests, m_failedTests, successRate);
        }
    }

    // 顯示性能測試結果
    void ShowPerformanceResults()
    {
        if(!m_performanceEnabled || ArraySize(m_performanceResults) == 0) return;

        Print("=== 性能測試結果 ===");
        for(int i = 0; i < ArraySize(m_performanceResults); i++)
        {
            PerformanceResult* result = m_performanceResults[i];
            if(result != NULL)
            {
                Print(StringFormat("%s: %.2f ms", result.GetTestName(), result.GetDurationMs()));
            }
        }
    }

    // 清理性能測試結果
    void CleanupPerformanceResults()
    {
        for(int i = 0; i < ArraySize(m_performanceResults); i++)
        {
            if(m_performanceResults[i] != NULL)
            {
                delete m_performanceResults[i];
                m_performanceResults[i] = NULL;
            }
        }
        ArrayResize(m_performanceResults, 0);
    }

    // 獲取測試統計
    int GetTotalTests() const { return m_totalTests; }
    int GetPassedTests() const { return m_passedTests; }
    int GetFailedTests() const { return m_failedTests; }
    bool AllTestsPassed() const { return m_failedTests == 0 && m_totalTests > 0; }
};

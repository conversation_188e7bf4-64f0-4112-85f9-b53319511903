//+------------------------------------------------------------------+
//|                                        TestAllContainerFeatures.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../RunAllTests.mqh"
#include "TestTradingPipelineContainerIntegration.mqh"

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("\n" + StringRepeat("=", 100));
    Print("  TradingPipelineContainer 完整功能測試套件");
    Print(StringRepeat("=", 100));
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 100));

    // 選項1：運行完整的測試套件（包括單元測試和整合測試）
    RunCompleteContainerTestSuite();

    // 選項2：只運行容器相關測試
    // RunContainerOnlyTests();

    // 選項3：運行性能基準測試
    // RunPerformanceBenchmark();

    Print("\n" + StringRepeat("=", 100));
    Print("  TradingPipelineContainer 完整功能測試套件完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 100));
}

//+------------------------------------------------------------------+
//| 運行完整的容器測試套件                                             |
//+------------------------------------------------------------------+
void RunCompleteContainerTestSuite()
{
    Print("\n🚀 開始運行完整的容器測試套件...");

    // 1. 運行容器單元測試
    Print("\n--- 第1階段：容器單元測試 ---");
    RunTestTradingPipelineContainer();
    RunTestTradingPipelineContainerManager();

    // 2. 運行簡化整合測試
    Print("\n--- 第2階段：簡化整合測試 ---");
    SimpleContainerIntegrationTestRunner* simpleRunner = new SimpleContainerIntegrationTestRunner();
    simpleRunner.RunIntegrationTests();
    delete simpleRunner;

    // 3. 運行完整整合測試
    Print("\n--- 第3階段：完整整合測試 ---");
    TradingPipelineContainerIntegrationTest* fullRunner = new TradingPipelineContainerIntegrationTest();
    fullRunner.RunAllIntegrationTests();
    delete fullRunner;

    Print("✅ 完整容器測試套件執行完成");
}

//+------------------------------------------------------------------+
//| 只運行容器相關測試                                                 |
//+------------------------------------------------------------------+
void RunContainerOnlyTests()
{
    Print("\n🎯 開始運行容器專項測試...");

    // 運行容器單元測試
    Print("\n--- 容器單元測試 ---");
    RunTestTradingPipelineContainer();
    RunTestTradingPipelineContainerManager();

    // 運行容器整合測試
    Print("\n--- 容器整合測試 ---");
    RunTradingPipelineContainerIntegrationTests();

    Print("✅ 容器專項測試執行完成");
}

//+------------------------------------------------------------------+
//| 運行性能基準測試                                                   |
//+------------------------------------------------------------------+
void RunPerformanceBenchmark()
{
    Print("\n⚡ 開始運行性能基準測試...");

    TradingPipelineContainerIntegrationTest* perfTest = new TradingPipelineContainerIntegrationTest();

    // 只運行性能相關的測試
    Print("\n--- 大規模場景性能測試 ---");
    // 注意：這裡需要修改 TradingPipelineContainerIntegrationTest 類來支持單獨運行特定測試
    // 或者創建專門的性能測試方法

    // 創建性能測試管理器
    TradingPipelineContainerManager* perfManager = new TradingPipelineContainerManager(
        "性能基準管理器", "BenchmarkManager", false, 100);

    const int containerCount = 50;
    const int pipelinesPerContainer = 20;
    const int iterations = 3;

    Print(StringFormat("  性能基準參數: %d容器 x %d流水線 x %d迭代", 
          containerCount, pipelinesPerContainer, iterations));

    // 創建大量容器和流水線
    uint setupStart = GetTickCount();
    for(int i = 0; i < containerCount; i++)
    {
        string containerName = StringFormat("基準容器_%d", i + 1);
        TradingPipelineContainer* container = new TradingPipelineContainer(
            containerName, "性能基準測試", "BenchmarkContainer", TRADING_TICK);

        for(int j = 0; j < pipelinesPerContainer; j++)
        {
            string pipelineName = StringFormat("基準流水線_%d_%d", i + 1, j + 1);
            MockTradingPipeline* pipeline = new MockTradingPipeline(pipelineName, true, 1);
            container.AddPipeline(pipeline);
        }

        perfManager.AddContainer(container);
    }
    uint setupEnd = GetTickCount();

    Print(StringFormat("  設置時間: %d 毫秒", setupEnd - setupStart));

    // 性能基準測試
    uint totalTime = 0;
    uint minTime = UINT_MAX;
    uint maxTime = 0;

    for(int iter = 0; iter < iterations; iter++)
    {
        perfManager.RestoreAll();
        
        uint startTime = GetTickCount();
        perfManager.ExecuteAll();
        uint endTime = GetTickCount();
        
        uint iterTime = endTime - startTime;
        totalTime += iterTime;
        
        if(iterTime < minTime) minTime = iterTime;
        if(iterTime > maxTime) maxTime = iterTime;
        
        Print(StringFormat("    基準迭代 %d: %d 毫秒", iter + 1, iterTime));
    }

    double avgTime = (double)totalTime / iterations;
    int totalPipelines = containerCount * pipelinesPerContainer;

    Print(StringFormat("  性能基準結果:"));
    Print(StringFormat("    總流水線數: %d", totalPipelines));
    Print(StringFormat("    平均執行時間: %.2f 毫秒", avgTime));
    Print(StringFormat("    最快執行時間: %d 毫秒", minTime));
    Print(StringFormat("    最慢執行時間: %d 毫秒", maxTime));
    Print(StringFormat("    平均每流水線時間: %.4f 毫秒", avgTime / totalPipelines));
    Print(StringFormat("    吞吐量: %.2f 流水線/秒", totalPipelines * 1000.0 / avgTime));

    // 性能評估
    if(avgTime < 10000) // 10秒內完成
    {
        Print("✅ 性能基準：優秀");
    }
    else if(avgTime < 20000) // 20秒內完成
    {
        Print("⚠️ 性能基準：良好");
    }
    else
    {
        Print("❌ 性能基準：需要優化");
    }

    delete perfManager;
    delete perfTest;

    Print("✅ 性能基準測試執行完成");
}

//+------------------------------------------------------------------+
//| 運行快速驗證測試                                                   |
//+------------------------------------------------------------------+
void RunQuickValidation()
{
    Print("\n⚡ 開始快速驗證測試...");

    // 創建簡單的驗證場景
    TradingPipelineContainerManager* quickManager = new TradingPipelineContainerManager("快速驗證管理器");

    // 創建基本容器
    TradingPipelineContainer* quickContainer = new TradingPipelineContainer(
        "快速驗證容器", "用於快速驗證", "QuickContainer", TRADING_TICK);

    // 添加幾個流水線
    MockTradingPipeline* pipeline1 = new MockTradingPipeline("快速流水線1", true, 5);
    MockTradingPipeline* pipeline2 = new MockTradingPipeline("快速流水線2", true, 10);

    quickContainer.AddPipeline(pipeline1);
    quickContainer.AddPipeline(pipeline2);
    quickManager.AddContainer(quickContainer);

    // 執行驗證
    uint startTime = GetTickCount();
    quickManager.ExecuteAll();
    uint endTime = GetTickCount();

    // 驗證結果
    bool success = quickManager.IsExecuted() && 
                   quickContainer.IsExecuted() && 
                   pipeline1.GetExecutionCount() == 1 && 
                   pipeline2.GetExecutionCount() == 1;

    Print(StringFormat("  快速驗證結果: %s (耗時: %d 毫秒)", 
          success ? "✅ 通過" : "❌ 失敗", endTime - startTime));

    delete quickManager;

    Print("✅ 快速驗證測試執行完成");
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}

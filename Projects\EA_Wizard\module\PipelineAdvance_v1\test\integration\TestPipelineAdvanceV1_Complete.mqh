//+------------------------------------------------------------------+
//|                           TestPipelineAdvanceV1_Complete.mqh    |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineRegistry.mqh"
#include "../../TradingPipelineExplorer.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單交易流水線                                           |
//+------------------------------------------------------------------+
class TestCompleteTradingPipeline : public TradingPipeline
{
private:
    string m_testMessage;
    bool m_shouldFail;
    int m_executionCount;

public:
    TestCompleteTradingPipeline(string name, string testMessage = "測試執行", bool shouldFail = false)
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage),
          m_shouldFail(shouldFail),
          m_executionCount(0)
    {
    }

    int GetExecutionCount() const { return m_executionCount; }

protected:
    virtual void Main() override
    {
        m_executionCount++;
        if(m_shouldFail)
        {
            Print(StringFormat("[%s] 模擬執行失敗: %s (執行次數: %d)", GetName(), m_testMessage, m_executionCount));
        }
        else
        {
            Print(StringFormat("[%s] 執行成功: %s (執行次數: %d)", GetName(), m_testMessage, m_executionCount));
        }
    }
};

//+------------------------------------------------------------------+
//| PipelineAdvance_v1 完整整合測試類                               |
//+------------------------------------------------------------------+
class TestPipelineAdvanceV1CompleteCase : public TestCase
{
private:
    TestRunner* m_runner;
    bool m_ownsRunner;

public:
    // 構造函數 - 可以接受外部 TestRunner 或創建內部 TestRunner
    TestPipelineAdvanceV1CompleteCase(TestRunner* externalRunner = NULL) : TestCase("TestPipelineAdvanceV1Complete")
    {
        if(externalRunner != NULL)
        {
            m_runner = externalRunner;
            m_ownsRunner = false;
        }
        else
        {
            m_runner = new TestRunner();
            m_ownsRunner = true;
        }
    }

    ~TestPipelineAdvanceV1CompleteCase()
    {
        if(m_ownsRunner && m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    virtual void RunTests() override
    {
        TestBasicWorkflow();
        TestRegistryExplorerIntegration();
        TestEventStageMapping();
        TestComplexScenario();
        TestErrorHandling();
        TestPerformanceScenario();

        // 只有當擁有內部 TestRunner 時才顯示摘要
        if(m_ownsRunner)
        {
            m_runner.ShowSummary();
        }
    }

protected:
    // 記錄測試結果的輔助方法
    void RecordResult(TestResult* result)
    {
        m_runner.RecordResult(result);
    }

    // 測試基本工作流程
    void TestBasicWorkflow()
    {
        Print("=== 測試基本工作流程 ===");

        // 1. 創建管理器
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("完整測試管理器");

        // 2. 創建註冊器
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "完整測試註冊器");

        // 3. 創建探索器
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "完整測試探索器");

        // 4. 創建容器和流水線
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("初始化容器", "處理初始化", "Container", TRADING_INIT);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("Tick容器", "處理Tick", "Container", TRADING_TICK);

        TestCompleteTradingPipeline* initPipeline = new TestCompleteTradingPipeline("初始化流水線", "基本工作流程初始化");
        TestCompleteTradingPipeline* tickPipeline = new TestCompleteTradingPipeline("Tick流水線", "基本工作流程Tick");

        // 5. 註冊容器
        bool regResult1 = registry.Register(TRADING_INIT, initContainer);
        bool regResult2 = registry.Register(TRADING_TICK, tickContainer);
        RecordResult(Assert::AssertTrue("基本_註冊初始化容器", regResult1));
        RecordResult(Assert::AssertTrue("基本_註冊Tick容器", regResult2));

        // 6. 添加流水線到容器
        initContainer.AddPipeline(initPipeline);
        tickContainer.AddPipeline(tickPipeline);

        // 7. 通過探索器查找
        ITradingPipeline* foundInit = explorer.GetPipeline(TRADING_INIT);
        ITradingPipeline* foundTick = explorer.GetPipeline(TRADING_TICK);
        RecordResult(Assert::AssertNotNull("基本_探索器找到初始化", foundInit));
        RecordResult(Assert::AssertNotNull("基本_探索器找到Tick", foundTick));

        // 8. 執行流水線
        if(foundInit != NULL) foundInit.Execute();
        if(foundTick != NULL) foundTick.Execute();

        // 9. 驗證執行狀態
        RecordResult(Assert::AssertTrue("基本_初始化已執行", foundInit != NULL && foundInit.IsExecuted()));
        RecordResult(Assert::AssertTrue("基本_Tick已執行", foundTick != NULL && foundTick.IsExecuted()));

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試註冊器和探索器的集成
    void TestRegistryExplorerIntegration()
    {
        Print("=== 測試註冊器和探索器的集成 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("集成測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "集成測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "集成測試探索器");

        // 創建多個容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("集成容器1", "集成測試1", "Container", TRADING_INIT);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("集成容器2", "集成測試2", "Container", TRADING_TICK);
        TradingPipelineContainer* container3 = new TradingPipelineContainer("集成容器3", "集成測試3", "Container", TRADING_DEINIT);

        // 註冊所有容器
        registry.Register(TRADING_INIT, container1);
        registry.Register(TRADING_TICK, container2);
        registry.Register(TRADING_DEINIT, container3);

        // 驗證註冊狀態
        RecordResult(Assert::AssertEquals("集成_註冊器事件數量", 3, registry.GetRegisteredEventCount()));
        RecordResult(Assert::AssertTrue("集成_初始化已註冊", registry.IsEventRegistered(TRADING_INIT)));
        RecordResult(Assert::AssertTrue("集成_Tick已註冊", registry.IsEventRegistered(TRADING_TICK)));
        RecordResult(Assert::AssertTrue("集成_清理已註冊", registry.IsEventRegistered(TRADING_DEINIT)));

        // 通過探索器查找所有容器
        ITradingPipeline* found1 = explorer.GetPipeline(TRADING_INIT);
        ITradingPipeline* found2 = explorer.GetPipeline(TRADING_TICK);
        ITradingPipeline* found3 = explorer.GetPipeline(TRADING_DEINIT);

        RecordResult(Assert::AssertNotNull("集成_探索器找到容器1", found1));
        RecordResult(Assert::AssertNotNull("集成_探索器找到容器2", found2));
        RecordResult(Assert::AssertNotNull("集成_探索器找到容器3", found3));

        // 驗證名稱匹配
        if(found1 != NULL) RecordResult(Assert::AssertEquals("集成_容器1名稱", "集成容器1", found1.GetName()));
        if(found2 != NULL) RecordResult(Assert::AssertEquals("集成_容器2名稱", "集成容器2", found2.GetName()));
        if(found3 != NULL) RecordResult(Assert::AssertEquals("集成_容器3名稱", "集成容器3", found3.GetName()));

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試事件和階段的映射
    void TestEventStageMapping()
    {
        Print("=== 測試事件和階段的映射 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("映射測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "映射測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "映射測試探索器");

        // 創建階段容器
        TradingPipelineContainer* stageContainer = new TradingPipelineContainer("階段容器", "階段測試", "Container", TRADING_INIT);

        // 註冊階段
        registry.Register(INIT_START, stageContainer);

        // 通過事件查找（應該能找到，因為 INIT_START 映射到 TRADING_INIT）
        ITradingPipeline* foundByEvent = explorer.GetPipeline(TRADING_INIT);
        ITradingPipeline* foundByStage = explorer.GetPipeline(INIT_START);

        RecordResult(Assert::AssertNotNull("映射_通過事件找到", foundByEvent));
        RecordResult(Assert::AssertNotNull("映射_通過階段找到", foundByStage));

        // 驗證是同一個容器
        if(foundByEvent != NULL && foundByStage != NULL)
        {
            RecordResult(Assert::AssertEquals("映射_事件階段名稱一致", foundByEvent.GetName(), foundByStage.GetName()));
        }

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試複雜場景
    void TestComplexScenario()
    {
        Print("=== 測試複雜場景 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("複雜測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "複雜測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "複雜測試探索器");

        // 創建複雜的容器結構
        TradingPipelineContainer* mainContainer = new TradingPipelineContainer("主容器", "主要處理容器", "Container", TRADING_TICK);

        // 創建多個子流水線
        TestCompleteTradingPipeline* pipeline1 = new TestCompleteTradingPipeline("數據處理", "複雜場景數據處理");
        TestCompleteTradingPipeline* pipeline2 = new TestCompleteTradingPipeline("信號分析", "複雜場景信號分析");
        TestCompleteTradingPipeline* pipeline3 = new TestCompleteTradingPipeline("風險控制", "複雜場景風險控制");
        TestCompleteTradingPipeline* pipeline4 = new TestCompleteTradingPipeline("訂單執行", "複雜場景訂單執行");

        // 添加所有子流水線
        mainContainer.AddPipeline(pipeline1);
        mainContainer.AddPipeline(pipeline2);
        mainContainer.AddPipeline(pipeline3);
        mainContainer.AddPipeline(pipeline4);

        // 註冊主容器
        registry.Register(TRADING_TICK, mainContainer);

        // 通過探索器查找並執行
        ITradingPipeline* found = explorer.GetPipeline(TRADING_TICK);
        RecordResult(Assert::AssertNotNull("複雜_找到主容器", found));

        if(found != NULL)
        {
            // 執行容器（應該執行所有子流水線）
            found.Execute();
            RecordResult(Assert::AssertTrue("複雜_主容器已執行", found.IsExecuted()));

            // 檢查容器內容
            TradingPipelineContainer* container = dynamic_cast<TradingPipelineContainer*>(found);
            if(container != NULL)
            {
                RecordResult(Assert::AssertEquals("複雜_子流水線數量", 4, container.GetPipelineCount()));
                RecordResult(Assert::AssertFalse("複雜_容器非空", container.IsEmpty()));
            }
        }

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("=== 測試錯誤處理 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("錯誤測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "錯誤測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "錯誤測試探索器");

        // 創建混合成功/失敗的流水線
        TradingPipelineContainer* container = new TradingPipelineContainer("錯誤測試容器", "錯誤處理測試", "Container", TRADING_INIT);

        TestCompleteTradingPipeline* successPipeline = new TestCompleteTradingPipeline("成功流水線", "應該成功", false);
        TestCompleteTradingPipeline* failPipeline = new TestCompleteTradingPipeline("失敗流水線", "應該失敗", true);
        TestCompleteTradingPipeline* anotherSuccessPipeline = new TestCompleteTradingPipeline("另一個成功流水線", "也應該成功", false);

        container.AddPipeline(successPipeline);
        container.AddPipeline(failPipeline);
        container.AddPipeline(anotherSuccessPipeline);

        // 註冊容器
        registry.Register(TRADING_INIT, container);

        // 執行並檢查結果
        ITradingPipeline* found = explorer.GetPipeline(TRADING_INIT);
        RecordResult(Assert::AssertNotNull("錯誤_找到容器", found));

        if(found != NULL)
        {
            found.Execute();
            RecordResult(Assert::AssertTrue("錯誤_容器已執行", found.IsExecuted()));

            // 檢查所有子流水線都被執行了（即使有失敗的）
            RecordResult(Assert::AssertEquals("錯誤_成功流水線執行次數", 1, successPipeline.GetExecutionCount()));
            RecordResult(Assert::AssertEquals("錯誤_失敗流水線執行次數", 1, failPipeline.GetExecutionCount()));
            RecordResult(Assert::AssertEquals("錯誤_另一個成功流水線執行次數", 1, anotherSuccessPipeline.GetExecutionCount()));
        }

        // 測試空查詢
        ITradingPipeline* notFound = explorer.GetPipeline(TRADING_DEINIT);
        RecordResult(Assert::AssertNull("錯誤_未註冊事件查詢", notFound));

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試性能場景
    void TestPerformanceScenario()
    {
        Print("=== 測試性能場景 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("性能測試管理器");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "性能測試註冊器");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "性能測試探索器");

        // 創建大量流水線的場景
        TradingPipelineContainer* performanceContainer = new TradingPipelineContainer("性能測試容器", "性能測試", "Container", TRADING_TICK);

        const int PIPELINE_COUNT = 20; // 創建20個流水線
        TestCompleteTradingPipeline* pipelines[20];

        // 創建並添加流水線
        for(int i = 0; i < PIPELINE_COUNT; i++)
        {
            string name = StringFormat("性能流水線_%d", i + 1);
            string message = StringFormat("性能測試流水線 %d", i + 1);
            pipelines[i] = new TestCompleteTradingPipeline(name, message);
            performanceContainer.AddPipeline(pipelines[i]);
        }

        // 註冊容器
        registry.Register(TRADING_TICK, performanceContainer);

        // 驗證容器狀態
        RecordResult(Assert::AssertEquals("性能_流水線數量", PIPELINE_COUNT, performanceContainer.GetPipelineCount()));
        RecordResult(Assert::AssertFalse("性能_容器非空", performanceContainer.IsEmpty()));

        // 通過探索器查找並執行
        ITradingPipeline* found = explorer.GetPipeline(TRADING_TICK);
        RecordResult(Assert::AssertNotNull("性能_找到容器", found));

        if(found != NULL)
        {
            // 記錄開始時間
            uint startTime = GetTickCount();

            // 執行容器
            found.Execute();

            // 記錄結束時間
            uint endTime = GetTickCount();
            uint executionTime = endTime - startTime;

            RecordResult(Assert::AssertTrue("性能_容器已執行", found.IsExecuted()));
            Print(StringFormat("性能測試：執行 %d 個流水線耗時 %d 毫秒", PIPELINE_COUNT, executionTime));

            // 驗證所有子流水線都被執行
            int executedCount = 0;
            for(int i = 0; i < PIPELINE_COUNT; i++)
            {
                if(pipelines[i].GetExecutionCount() > 0)
                {
                    executedCount++;
                }
            }
            RecordResult(Assert::AssertEquals("性能_所有流水線已執行", PIPELINE_COUNT, executedCount));
        }

        delete explorer;
        delete registry;
        delete manager;
    }
};

//+------------------------------------------------------------------+
//| 運行完整整合測試的函數                                           |
//+------------------------------------------------------------------+
void RunTestPipelineAdvanceV1Complete()
{
    Print("\n🧪 開始執行 PipelineAdvance_v1 完整整合測試...");

    TestPipelineAdvanceV1CompleteCase* testCase = new TestPipelineAdvanceV1CompleteCase();
    testCase.RunTests();
    delete testCase;

    Print("✅ PipelineAdvance_v1 完整整合測試執行完成\n");
}

//+------------------------------------------------------------------+
//| 運行完整整合測試（使用外部 TestRunner）                         |
//+------------------------------------------------------------------+
void RunTestPipelineAdvanceV1Complete(TestRunner* runner)
{
    if(runner == NULL)
    {
        RunTestPipelineAdvanceV1Complete();
        return;
    }

    Print("\n🧪 執行 PipelineAdvance_v1 完整整合測試（外部 TestRunner）...");

    TestPipelineAdvanceV1CompleteCase* testCase = new TestPipelineAdvanceV1CompleteCase(runner);
    testCase.RunTests();
    delete testCase;

    Print("✅ PipelineAdvance_v1 完整整合測試完成\n");
}
//+------------------------------------------------------------------+
//|                                      SimpleTestRunner_Updated.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipeline.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "MockTradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 簡化版整合測試運行器 (更新版)                                      |
//+------------------------------------------------------------------+
class SimpleIntegrationTestRunner_Updated : public TestRunner
{
public:
    // 構造函數
    SimpleIntegrationTestRunner_Updated() : TestRunner() {}

    // 運行整合測試
    void RunIntegrationTests()
    {
        Print("🚀 開始執行 PipelineAdvance_v1 整合測試 (更新版)...");

        // 運行基本工作流程測試
        TestBasicWorkflow();

        // 運行錯誤處理測試
        TestErrorHandling();

        // 運行邊界情況測試
        TestEdgeCases();

        // 運行 TradingPipelineContainerManager 測試
        TestTradingPipelineContainerManager();

        // 顯示摘要
        ShowSummary();

        Print("✅ PipelineAdvance_v1 整合測試執行完成");
    }

private:
    // 測試基本工作流程
    void TestBasicWorkflow()
    {
        Print("=== 測試基本工作流程 ===");

        // 創建基本流水線
        MockTradingPipeline* dataFeed = MockTradingPipelineFactory::CreateDataFeedPipeline();
        MockTradingPipeline* signal = MockTradingPipelineFactory::CreateSignalPipeline();
        MockTradingPipeline* order = MockTradingPipelineFactory::CreateOrderPipeline();

        // 創建容器流水線
        TradingPipelineContainer* mainContainer = new TradingPipelineContainer("主工作流程", "主要交易容器", "Container", TRADING_TICK);
        mainContainer.AddPipeline(dataFeed);
        mainContainer.AddPipeline(signal);
        mainContainer.AddPipeline(order);

        // 執行工作流程
        mainContainer.Execute();

        // 驗證結果
        RecordResult(Assert::AssertTrue("基本工作流程_容器執行成功", mainContainer.IsExecuted()));
        RecordResult(Assert::AssertTrue("基本工作流程_數據饋送執行", dataFeed.IsExecuted()));
        RecordResult(Assert::AssertTrue("基本工作流程_信號分析執行", signal.IsExecuted()));
        RecordResult(Assert::AssertTrue("基本工作流程_訂單處理執行", order.IsExecuted()));
        RecordResult(Assert::AssertEquals("基本工作流程_流水線數量", 3, mainContainer.GetPipelineCount()));

        // 清理
        delete mainContainer;
        delete dataFeed;
        delete signal;
        delete order;
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("=== 測試錯誤處理 ===");

        // 創建包含失敗流水線的容器
        TradingPipelineContainer* errorContainer = new TradingPipelineContainer("錯誤處理測試", "錯誤處理容器", "Container", TRADING_TICK);

        MockTradingPipeline* success1 = MockTradingPipelineFactory::CreateSuccessfulPipeline("成功1");
        MockTradingPipeline* failed = MockTradingPipelineFactory::CreateFailedPipeline("失敗");
        MockTradingPipeline* success2 = MockTradingPipelineFactory::CreateSuccessfulPipeline("成功2");

        errorContainer.AddPipeline(success1);
        errorContainer.AddPipeline(failed);
        errorContainer.AddPipeline(success2);

        // 執行包含錯誤的流水線
        errorContainer.Execute();

        // 驗證錯誤處理
        RecordResult(Assert::AssertTrue("錯誤處理_容器執行", errorContainer.IsExecuted()));
        RecordResult(Assert::AssertTrue("錯誤處理_成功1執行", success1.IsExecuted()));
        RecordResult(Assert::AssertTrue("錯誤處理_失敗流水線執行", failed.IsExecuted()));
        RecordResult(Assert::AssertTrue("錯誤處理_成功2執行", success2.IsExecuted()));

        // 清理
        delete errorContainer;
        delete success1;
        delete failed;
        delete success2;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("=== 測試邊界情況 ===");

        // 測試空容器
        TradingPipelineContainer* emptyContainer = new TradingPipelineContainer("空容器", "沒有流水線的容器", "Container", TRADING_TICK);
        emptyContainer.Execute();
        RecordResult(Assert::AssertTrue("邊界情況_空容器執行", emptyContainer.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界情況_空容器流水線數量", 0, emptyContainer.GetPipelineCount()));

        // 測試單個流水線的容器
        TradingPipelineContainer* singleContainer = new TradingPipelineContainer("單個容器", "單個流水線容器", "Container", TRADING_TICK);
        MockTradingPipeline* singlePipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("單獨");
        singleContainer.AddPipeline(singlePipeline);
        singleContainer.Execute();

        RecordResult(Assert::AssertTrue("邊界情況_單個容器執行", singleContainer.IsExecuted()));
        RecordResult(Assert::AssertTrue("邊界情況_單個子流水線執行", singlePipeline.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界情況_單個容器子數量", 1, singleContainer.GetPipelineCount()));

        // 測試重複執行防護
        singleContainer.Execute(); // 再次執行
        RecordResult(Assert::AssertTrue("邊界情況_重複執行防護", singleContainer.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界情況_重複執行後執行次數", 1, singlePipeline.GetExecutionCount()));

        // 測試重置後重新執行
        singleContainer.Restore();
        singlePipeline.Restore();
        singlePipeline.ResetExecutionCount();

        singleContainer.Execute();
        RecordResult(Assert::AssertTrue("邊界情況_重置後重新執行", singleContainer.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界情況_重置後執行次數", 1, singlePipeline.GetExecutionCount()));

        // 清理
        delete emptyContainer;
        delete singleContainer;
        delete singlePipeline;
    }

    // 測試 TradingPipelineContainerManager
    void TestTradingPipelineContainerManager()
    {
        Print("=== 測試 TradingPipelineContainerManager ===");

        // 創建管理器
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("整合測試管理器");

        // 驗證基本屬性
        RecordResult(Assert::AssertEquals("管理器_名稱", "整合測試管理器", manager.GetName()));
        RecordResult(Assert::AssertEquals("管理器_最大容器數", 10, manager.GetMaxContainers()));
        RecordResult(Assert::AssertEquals("管理器_初始容器數", 0, manager.GetContainerCount()));

        // 創建並添加容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("測試容器1", "第一個測試容器", "Container", false, 50);
        // 注意：AddContainer 方法未定義，使用 SetContainer 替代
        RecordResult(Assert::AssertTrue("管理器_添加容器1", manager.SetContainer(TRADING_INIT, container1)));
        RecordResult(Assert::AssertEquals("管理器_添加後容器數", 1, manager.GetContainerCount()));

        // 清理
        delete manager;
        delete container1;
    }

    // 測試大規模場景
    void TestLargeScaleScenario()
    {
        Print("=== 測試大規模場景 ===");

        // 創建大規模容器
        TradingPipelineContainer* largeContainer = new TradingPipelineContainer("大規模容器", "包含大量流水線的容器", "Container", false, 50);

        // 創建多個子容器
        const int CONTAINER_COUNT = 3;
        const int PIPELINES_PER_CONTAINER = 5;

        TradingPipelineContainer* subContainers[];
        ArrayResize(subContainers, CONTAINER_COUNT);

        for(int i = 0; i < CONTAINER_COUNT; i++)
        {
            string containerName = "大規模子容器_" + IntegerToString(i + 1);
            subContainers[i] = new TradingPipelineContainer(containerName, "子容器", "SubContainer", false, 50);

            // 每個子容器添加多個流水線
            for(int j = 0; j < PIPELINES_PER_CONTAINER; j++)
            {
                string pipelineName = "大規模流水線_" + IntegerToString(i + 1) + "_" + IntegerToString(j + 1);
                MockTradingPipeline* pipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline(pipelineName, 5);
                subContainers[i].AddPipeline(pipeline);
            }

            largeContainer.AddPipeline(subContainers[i]);
        }

        // 執行大規模場景
        datetime startTime = TimeCurrent();
        largeContainer.Execute();
        datetime endTime = TimeCurrent();

        // 驗證大規模執行
        RecordResult(Assert::AssertTrue("大規模場景_容器執行完成", largeContainer.IsExecuted()));
        RecordResult(Assert::AssertEquals("大規模場景_子容器數量", CONTAINER_COUNT, largeContainer.GetPipelineCount()));

        // 驗證每個子容器
        for(int i = 0; i < CONTAINER_COUNT; i++)
        {
            string testName = "大規模場景_子容器" + IntegerToString(i + 1) + "執行";
            RecordResult(Assert::AssertTrue(testName, subContainers[i].IsExecuted()));

            string countTestName = "大規模場景_子容器" + IntegerToString(i + 1) + "流水線數量";
            RecordResult(Assert::AssertEquals(countTestName, PIPELINES_PER_CONTAINER, subContainers[i].GetPipelineCount()));
        }

        Print("大規模場景執行時間: " + IntegerToString(endTime - startTime) + " 秒");

        // 清理
        delete largeContainer;
        for(int i = 0; i < CONTAINER_COUNT; i++)
        {
            delete subContainers[i];
        }
    }
};

//+------------------------------------------------------------------+
//|                                      SimpleTestRunner_v2_Updated.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipeline.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "MockTradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 簡化版整合測試運行器 v2 (更新版)                                  |
//+------------------------------------------------------------------+
class SimpleIntegrationTestRunner_v2_Updated : public TestRunner
{
public:
    // 構造函數
    SimpleIntegrationTestRunner_v2_Updated() : TestRunner() {}

    // 運行整合測試
    void RunIntegrationTests()
    {
        Print("🚀 開始執行 PipelineAdvance_v1 整合測試 v2 (更新版)...");

        // 運行基本工作流程測試
        TestBasicWorkflow();

        // 運行 TradingPipelineContainerManager 測試
        TestTradingPipelineContainerManager();

        // 顯示摘要
        ShowSummary();

        Print("✅ PipelineAdvance_v1 整合測試執行完成");
    }

private:
    // 測試基本工作流程
    void TestBasicWorkflow()
    {
        Print("=== 測試基本工作流程 ===");

        // 創建基本流水線
        MockTradingPipeline* dataFeed = MockTradingPipelineFactory::CreateDataFeedPipeline();
        MockTradingPipeline* signal = MockTradingPipelineFactory::CreateSignalPipeline();

        // 創建容器流水線
        TradingPipelineContainer* mainContainer = new TradingPipelineContainer("主工作流程", "主要交易容器", "Container", TRADING_TICK);
        mainContainer.AddPipeline(dataFeed);
        mainContainer.AddPipeline(signal);

        // 執行工作流程
        mainContainer.Execute();

        // 驗證結果
        RecordResult(Assert::AssertTrue("基本工作流程_容器執行成功", mainContainer.IsExecuted()));

        // 清理
        delete mainContainer;
        delete dataFeed;
        delete signal;
    }

    // 測試 TradingPipelineContainerManager
    void TestTradingPipelineContainerManager()
    {
        Print("=== 測試 TradingPipelineContainerManager ===");

        // 創建管理器
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("整合測試管理器");

        // 驗證基本屬性
        RecordResult(Assert::AssertEquals("管理器_名稱", "整合測試管理器", manager.GetName()));
        RecordResult(Assert::AssertEquals("管理器_類型", "ContainerManager", manager.GetType()));
        RecordResult(Assert::AssertEquals("管理器_最大容器數", 10, manager.GetMaxContainers()));
        RecordResult(Assert::AssertEquals("管理器_初始容器數", 0, manager.GetContainerCount()));

        // 創建並添加容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("測試容器1", "第一個測試容器", "Container", false, 50);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("測試容器2", "第二個測試容器", "Container", false, 50);

        // 注意：AddContainer 方法未定義，使用 SetContainer 替代
        RecordResult(Assert::AssertTrue("管理器_添加容器1", manager.SetContainer(TRADING_INIT, container1)));
        RecordResult(Assert::AssertTrue("管理器_添加容器2", manager.SetContainer(TRADING_TICK, container2)));
        RecordResult(Assert::AssertEquals("管理器_添加後容器數", 2, manager.GetContainerCount()));

        // 測試事件驅動執行
        TestEventDrivenExecution(manager, container1, container2);

        // 清理
        delete manager;
        delete container1;
        delete container2;
    }

    // 測試事件驅動執行
    void TestEventDrivenExecution(TradingPipelineContainerManager* manager,
                                  TradingPipelineContainer* initContainer,
                                  TradingPipelineContainer* tickContainer)
    {
        Print("--- 測試事件驅動執行 ---");

        // 為容器添加流水線
        MockTradingPipeline* initPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("初始化流水線");
        MockTradingPipeline* tickPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("Tick流水線");

        initContainer.AddPipeline(initPipeline);
        tickContainer.AddPipeline(tickPipeline);

        // 測試 TRADING_INIT 事件執行
        manager.Execute(TRADING_INIT);
        RecordResult(Assert::AssertTrue("事件執行_INIT容器執行", initContainer.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件執行_TICK容器未執行", tickContainer.IsExecuted()));

        // 注意：RestoreAll 方法未定義，使用 Restore 替代
        manager.Restore(TRADING_INIT);

        // 測試 TRADING_TICK 事件執行
        manager.Execute(TRADING_TICK);
        RecordResult(Assert::AssertFalse("事件執行_INIT容器重置後未執行", initContainer.IsExecuted()));
        RecordResult(Assert::AssertTrue("事件執行_TICK容器執行", tickContainer.IsExecuted()));

        // 清理
        delete initPipeline;
        delete tickPipeline;
    }
};

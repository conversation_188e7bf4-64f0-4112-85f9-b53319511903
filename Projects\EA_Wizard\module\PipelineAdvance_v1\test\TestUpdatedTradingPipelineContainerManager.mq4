//+------------------------------------------------------------------+
//|                    TestUpdatedTradingPipelineContainerManager.mq4 |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineContainerManager.mqh"

//+------------------------------------------------------------------+
//| 測試更新後的 TradingPipelineContainerManager                    |
//+------------------------------------------------------------------+
void <PERSON>Start()
{
    Print("=== 測試更新後的 TradingPipelineContainerManager ===");
    
    // 創建管理器
    TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("測試管理器", "TestManager");
    
    // 創建容器
    TradingPipelineContainer* initContainer = new TradingPipelineContainer("初始化容器", "處理初始化事件");
    TradingPipelineContainer* tickContainer = new TradingPipelineContainer("Tick容器", "處理Tick事件");
    TradingPipelineContainer* deinitContainer = new TradingPipelineContainer("清理容器", "處理清理事件");
    
    Print("\n--- 測試 1: SetContainer 方法 ---");
    bool result1 = manager.SetContainer(TRADING_INIT, initContainer);
    bool result2 = manager.SetContainer(TRADING_TICK, tickContainer);
    bool result3 = manager.SetContainer(TRADING_DEINIT, deinitContainer);
    
    Print("設置 INIT 容器: ", result1 ? "成功" : "失敗");
    Print("設置 TICK 容器: ", result2 ? "成功" : "失敗");
    Print("設置 DEINIT 容器: ", result3 ? "成功" : "失敗");
    Print("容器數量: ", manager.GetContainerCount());
    
    Print("\n--- 測試 2: GetContainer 方法 ---");
    TradingPipelineContainer* retrievedInit = manager.GetContainer(TRADING_INIT);
    TradingPipelineContainer* retrievedTick = manager.GetContainer(TRADING_TICK);
    TradingPipelineContainer* retrievedDeinit = manager.GetContainer(TRADING_DEINIT);
    
    Print("獲取 INIT 容器: ", retrievedInit != NULL ? retrievedInit.GetName() : "NULL");
    Print("獲取 TICK 容器: ", retrievedTick != NULL ? retrievedTick.GetName() : "NULL");
    Print("獲取 DEINIT 容器: ", retrievedDeinit != NULL ? retrievedDeinit.GetName() : "NULL");
    
    Print("\n--- 測試 3: GetAllEvents 方法 ---");
    ENUM_TRADING_EVENT events[];
    int eventCount = manager.GetAllEvents(events);
    Print("事件數量: ", eventCount);
    for(int i = 0; i < eventCount; i++)
    {
        Print("事件 ", i, ": ", TradingEventUtils::EventToString(events[i]));
    }
    
    Print("\n--- 測試 4: GetAllContainers 方法 ---");
    TradingPipelineContainer* containers[];
    int containerCount = manager.GetAllContainers(containers);
    Print("容器數量: ", containerCount);
    for(int i = 0; i < containerCount; i++)
    {
        if(containers[i] != NULL)
        {
            Print("容器 ", i, ": ", containers[i].GetName());
        }
    }
    
    Print("\n--- 測試 5: Execute 方法 ---");
    Print("執行前管理器狀態: ", manager.IsExecuted() ? "已執行" : "未執行");
    manager.Execute(TRADING_INIT);
    Print("執行 INIT 後: ", manager.IsExecuted() ? "已執行" : "未執行");
    manager.Execute(TRADING_TICK);
    Print("執行 TICK 後: ", manager.IsExecuted() ? "已執行" : "未執行");
    manager.Execute(TRADING_DEINIT);
    Print("執行 DEINIT 後: ", manager.IsExecuted() ? "已執行" : "未執行");
    
    Print("\n--- 測試 6: Restore 方法 ---");
    manager.Restore(TRADING_INIT);
    Print("重置 INIT 後: ", manager.IsExecuted() ? "已執行" : "未執行");
    manager.Restore(TRADING_TICK);
    Print("重置 TICK 後: ", manager.IsExecuted() ? "已執行" : "未執行");
    manager.Restore(TRADING_DEINIT);
    Print("重置 DEINIT 後: ", manager.IsExecuted() ? "已執行" : "未執行");
    
    Print("\n--- 測試 7: RemoveContainer 方法 ---");
    bool removeResult1 = manager.RemoveContainer(initContainer);
    Print("按對象移除 INIT 容器: ", removeResult1 ? "成功" : "失敗");
    Print("移除後容器數量: ", manager.GetContainerCount());
    
    bool removeResult2 = manager.RemoveContainer("Tick容器");
    Print("按名稱移除 TICK 容器: ", removeResult2 ? "成功" : "失敗");
    Print("移除後容器數量: ", manager.GetContainerCount());
    
    Print("\n--- 測試 8: 狀態信息 ---");
    Print("管理器狀態信息:");
    Print(manager.GetStatusInfo());
    
    Print("\n--- 測試 9: 狀態檢查方法 ---");
    Print("管理器名稱: ", manager.GetName());
    Print("管理器類型: ", manager.GetType());
    Print("是否啟用: ", manager.IsEnabled() ? "是" : "否");
    Print("是否為空: ", manager.IsEmpty() ? "是" : "否");
    Print("是否已滿: ", manager.IsFull() ? "是" : "否");
    Print("是否有空位: ", manager.HasEmptySlot() ? "是" : "否");
    Print("最大容器數: ", manager.GetMaxContainers());
    
    Print("\n--- 測試 10: Clear 方法 ---");
    manager.Clear();
    Print("清理後容器數量: ", manager.GetContainerCount());
    Print("清理後是否為空: ", manager.IsEmpty() ? "是" : "否");
    
    // 清理
    delete manager;
    delete initContainer;
    delete tickContainer;
    delete deinitContainer;
    
    Print("\n=== 測試完成 ===");
}
//+------------------------------------------------------------------+

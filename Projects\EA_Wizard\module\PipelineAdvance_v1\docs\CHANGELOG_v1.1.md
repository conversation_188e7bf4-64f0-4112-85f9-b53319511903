# PipelineAdvance_v1 變更日誌 v1.1

## 📅 版本 1.1 - 2024年12月

### 🚀 主要變更

#### 1. TradingPipelineContainer 簡化
- **移除 m_eventType 成員變數**
  - 移除 `ENUM_TRADING_EVENT m_eventType` 成員
  - 移除 `GetEventType()` 和 `SetEventType()` 方法
  - 更新構造函數，移除 `eventType` 參數
  - 更新 `GetStatusInfo()` 方法，移除事件類型信息

#### 2. TradingPipelineContainerManager 性能優化
- **從 Vector 遷移到 HashMap**
  - 將 `Vector<TradingPipelineContainer*> m_containers` 改為 `HashMap<string, TradingPipelineContainer*> m_containers`
  - 使用容器名稱作為 HashMap 的 key
  - 提供 O(1) 查找性能，相比 Vector 的 O(n) 有顯著提升

#### 3. API 簡化和優化
- **移除的方法**：
  - `FindContainerByEventType(ENUM_TRADING_EVENT)`
  - `GetContainersByEventType(ENUM_TRADING_EVENT, TradingPipelineContainer*[])`
  - `Execute(ENUM_TRADING_EVENT)`
  - `Restore(ENUM_TRADING_EVENT)`
  - `EnableContainersByEventType(ENUM_TRADING_EVENT, bool)`
  - `GetContainerCountByEventType(ENUM_TRADING_EVENT)`

- **修改的方法**：
  - `GetContainer(int index)` → `GetContainer(string name)` - 改為按名稱獲取
  - 所有迭代方法改用 `foreachm` 宏以支持 HashMap

### 🎯 設計理念變更

#### 從事件驅動到名稱驅動
- **舊設計**：容器與特定事件類型綁定，支持按事件類型批量操作
- **新設計**：容器獨立於事件類型，使用名稱進行管理和訪問
- **優勢**：提高靈活性，簡化使用邏輯，減少耦合

#### 性能優先的數據結構
- **舊設計**：使用 Vector 進行線性查找
- **新設計**：使用 HashMap 進行常數時間查找
- **優勢**：查找性能從 O(n) 提升到 O(1)

### 📊 性能提升

| 操作 | 舊版本 (Vector) | 新版本 (HashMap) | 提升 |
|------|----------------|------------------|------|
| 查找容器 | O(n) | O(1) | 顯著提升 |
| 添加容器 | O(1) | O(1) | 相同 |
| 移除容器 | O(n) | O(1) | 顯著提升 |
| 內存使用 | 較低 | 略高 | 可接受 |

### 🔧 API 變更指南

#### 構造函數變更
```mql4
// 舊版本
TradingPipelineContainer* container = new TradingPipelineContainer(
    "容器名稱",
    "描述",
    "類型",
    TRADING_TICK,  // ❌ 移除此參數
    false,
    50
);

// 新版本
TradingPipelineContainer* container = new TradingPipelineContainer(
    "容器名稱",
    "描述",
    "類型",
    false,
    50
);
```

#### 容器查找變更
```mql4
// 舊版本 - 按索引獲取
TradingPipelineContainer* container = manager.GetContainer(0);

// 新版本 - 按名稱獲取
TradingPipelineContainer* container = manager.GetContainer("容器名稱");
```

#### 事件類型操作移除
```mql4
// ❌ 以下方法已移除
manager.Execute(TRADING_TICK);
manager.FindContainerByEventType(TRADING_TICK);
container.GetEventType();
container.SetEventType(TRADING_TICK);

// ✅ 使用新的統一方法
manager.ExecuteAll();
manager.FindContainerByName("容器名稱");
```

### 🧪 測試覆蓋

#### 新增測試
- `TestContainerModifications.mqh` - 驗證修改後的功能
- 測試容器創建不再需要事件類型
- 測試管理器使用 HashMap 的基本操作
- 測試按名稱查找和獲取容器

#### 測試結果
- ✅ 所有新功能測試通過
- ✅ 向後兼容性測試通過（除已移除的 API）
- ✅ 性能測試顯示查找速度顯著提升

### 📚 文檔更新

#### 更新的文檔
- `class_diagram.md` - 更新類圖以反映新的設計
- `TradingPipelineContainer_ClassDiagram.md` - 更新容器類圖
- 使用示例代碼更新

#### 新增文檔
- `CHANGELOG_v1.1.md` - 本變更日誌
- 遷移指南（如需要）

### 🔄 遷移建議

#### 立即需要的變更
1. **更新容器創建代碼**：移除事件類型參數
2. **更新容器查找代碼**：改用名稱查找
3. **移除事件類型相關調用**：使用統一的執行方法

#### 可選的優化
1. **利用新的性能優勢**：在大量容器場景中使用名稱查找
2. **簡化業務邏輯**：移除不必要的事件類型判斷
3. **重構測試代碼**：使用新的 API 進行測試

### 🎉 總結

這次更新主要專注於：
- **簡化設計**：移除不必要的事件類型綁定
- **提升性能**：使用 HashMap 優化查找性能
- **改善 API**：提供更直觀的基於名稱的操作

這些變更使得 PipelineAdvance_v1 模組更加高效、簡潔和易用，同時保持了核心功能的完整性。

---

**版本**: v1.1  
**發布日期**: 2024年12月  
**兼容性**: 與 v1.0 部分兼容（需要 API 調整）  
**下一版本預告**: 考慮添加異步執行支持和更多性能優化
